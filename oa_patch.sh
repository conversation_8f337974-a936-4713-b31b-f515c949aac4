#!/bin/bash
# 目标文件
TARGET_FILE="/data/weaver/nginx/build/layout/static/js/main.js"

# 检查是否已经存在 addPeopleMenu 函数
if grep -q "function addPeopleMenu()" "$TARGET_FILE"; then
    echo "addPeopleMenu 函数已存在，跳过追加。"
    exit 0
fi

# 要追加的完整 JS 代码
cat <<'EOF' >> "$TARGET_FILE"

function addPeopleMenu() {
  var headerMenuCustomDom = document.getElementsByClassName('e10header-quick')[0];
  var peopleHtml = document.createElement('div');
  peopleHtml.innerHTML = `<div id="oamenuitem" title="People" class="e10header-menu-item" style="background: #353d50;">
    <a class="e10header-menu-item-wrapper" href=" " title="People" style="display: flex;align-items: center;">
      <span class="ebcoms-assets-icon ebcoms-assets-item  sm bg" style="color: rgb(0, 103, 255); width: 24px; height: 24px; background-color: transparent; border-color: transparent;">
        <span class="ui-icon ui-icon-wrapper">
          <svg t="1757387016411" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4177" width="200" height="200">
            <path d="M488.727273 1024c-25.6 0-46.545455-19.316364-46.545455-42.961455v-193.349818c0-23.645091 20.945455-42.961455 46.545455-42.961454s46.545455 19.316364 46.545454 42.961454v193.349818c0 23.645091-20.945455 42.961455-46.545454 42.961455zM512 605.090909C344.436364 605.090909 209.454545 470.109091 209.454545 302.545455S344.436364 0 512 0 814.545455 134.981818 814.545455 302.545455 679.563636 605.090909 512 605.090909z m0-512C395.636364 93.090909 302.545455 186.181818 302.545455 302.545455S395.636364 512 512 512 721.454545 418.909091 721.454545 302.545455 628.363636 93.090909 512 93.090909z" fill="#ffffff" p-id="4178"></path>
          </svg>
        </span>
      </span>
      <span class="e10header-app-menu-item-text">People+</span>
    </a>
  </div>
  <div id="oamenuitem2" title="$" class="e10header-menu-item" style="background: #353d50;">
    <a class="e10header-menu-item-wrapper" href="javascript:void(0)" title="$" style="display: flex;align-items: center;">
      <span class="ebcoms-assets-icon ebcoms-assets-item  sm bg" style="color: rgb(0, 103, 255); width: 24px; height: 24px; background-color: transparent; border-color: transparent;">
        <span class="ui-icon ui-icon-wrapper">
          <svg t="1757669164478" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15126" width="200" height="200"></svg>
        </span>
      </span>
    </a>
  </div>`;
  if (headerMenuCustomDom) {
      headerMenuCustomDom.insertAdjacentHTML('beforeBegin', peopleHtml.innerHTML);
  }
}

function setOAMenu () {
  let menu = document.getElementById("oamenuitem");
  if (!menu) {
    addPeopleMenu();
    return;
  } else {
    clearInterval(j);
    j = null;
    menu.onclick = function(e) {
      e.preventDefault();
      e.stopPropagation();
      sendRequest();
    }
  }
}

function setOAMenu2 () {
  let menu = document.getElementById("oamenuitem2");
  if (!menu) {
    addPeopleMenu();
    return;
  } else {
    clearInterval(jj);
    jj = null;
    menu.onclick = function(e) {
      e.preventDefault();
      e.stopPropagation();
      sendRequest2();
    }
  }
}

let j = setInterval(setOAMenu, 500);
let jj = setInterval(setOAMenu2, 500);

function getLocalId () {
  const stored = localStorage.getItem('E10_TEAMS_LOCAL');
  let userId = null;
  if (stored) {
    try {
      const parsed = JSON.parse(stored);
      userId = parsed?.currentUser?.id ?? null;
    } catch (e) {
      console.error("Failed to parse E10_TEAMS_LOCAL:", e);
    }
  }
  return userId;
}

function isValidUrl(string) {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

async function sendRequest() {
  const employeeid = getLocalId();
  if (employeeid === null) return;
  try {
    const base64EmployeeId = btoa(employeeid);
    const response = await fetch('https://connect.be.co:4000/jwt/new', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ employeeid: base64EmployeeId })
    });
    if (response.ok) {
      const responseData = await response.text();
      if (responseData === "no record found.") {
        showResult('未找到记录', 'error');
      } else if (isValidUrl(responseData)) {
        window.open(responseData, '_blank');
      }
    }
  } catch (error) { return }
}

async function sendRequest2() {
  const employeeid = getLocalId();
  if (employeeid === null) return;
  try {
    const base64EmployeeId = btoa(employeeid);
    window.open('https://connect.be.co:4000/waiscz_pay?id=' + base64EmployeeId, 'location=no');
  } catch (error) { return }
}

EOF

echo "JS 代码已成功追加到 $TARGET_FILE"

# 第二个目标文件
PRINT_TARGET_FILE="/data/weaver/nginx/build/print/static/js/print_main_page.js"

# 检查是否已经存在 hideLogos 函数
if grep -q "function hideLogos()" "$PRINT_TARGET_FILE"; then
    echo "hideLogos 函数已存在，跳过追加。"
else
    # 要追加的 JS 代码
    cat <<'EOF2' >> "$PRINT_TARGET_FILE"

const targetSelector = ".weapp-form-content tr:nth-child(2)"; // 第二个 <tr>，如果每个表格都可能有

// 封装隐藏逻辑
function hideLogos() {
  // querySelectorAll 找到所有符合条件且未隐藏的元素
  const logos = document.querySelectorAll(targetSelector);
  logos.forEach(logo => {
    if (!logo.hidden) {
      logo.hidden = true;
      console.log("元素已隐藏:", logo);
    }
  });
}

// 先执行一次
hideLogos();

// 创建 MutationObserver 持续监听 DOM 变化
const observer = new MutationObserver(hideLogos);

observer.observe(document.body, {
  childList: true,
  subtree: true
});

// 可选：如果担心页面长期存在性能问题，可以设置超时停止
setTimeout(() => observer.disconnect(), 60000); // 1分钟后断开监听

EOF2

    echo "JS 代码已成功追加到 $PRINT_TARGET_FILE"
fi