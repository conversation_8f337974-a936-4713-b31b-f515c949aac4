!function(e,n){if("object"===typeof exports&&"object"===typeof module)module.exports=n();else if("function"===typeof define&&define.amd)define([],n);else{var t=n();for(var r in t)("object"===typeof exports?exports:e)[r]=t[r]}}(window,(function(){return function(e){function n(n){for(var r,o,a=n[0],c=n[1],l=n[2],d=0,f=[];d<a.length;d++)o=a[d],Object.prototype.hasOwnProperty.call(u,o)&&u[o]&&f.push(u[o][0]),u[o]=0;for(r in c)Object.prototype.hasOwnProperty.call(c,r)&&(e[r]=c[r]);for(n.originChunkName=n.originChunkName||"main",s&&s(n);f.length;)f.shift()();return i.push.apply(i,l||[]),t()}function t(){for(var e,n=0;n<i.length;n++){for(var t=i[n],r=!0,o=1;o<t.length;o++){var c=t[o];0!==u[c]&&(r=!1)}r&&(i.splice(n--,1),e=a(a.s=t[0]))}return e}var r={},o={7:0},u={7:0},i=[];function a(n){if(r[n])return r[n].exports;var t=r[n]={i:n,l:!1,exports:{}};return e[n].call(t.exports,t,t.exports,a),t.l=!0,t.exports}a.e=function(e){var n=[];o[e]?n.push(o[e]):0!==o[e]&&{1:1,2:1,3:1,4:1,5:1,6:1}[e]&&n.push(o[e]=new Promise((function(n,t){for(var r="static/css/"+({1:"component_mobile~component_mobile2",2:"component_mobile",3:"component_mobile2",4:"layout_PCFirstPage",5:"layout_components",6:"layout_components2"}[e]||e)+".css?v="+{1:"a4979ccb",2:"929eb2c0",3:"52acc116",4:"dac4ce08",5:"fe36d371",6:"cef75433"}[e],u=(window[a.k]||window.publicDomainstatic||"")+(window[a.z]||window.publicUrlstatic||window.publicUrl||"")+a.p+r,i=document.getElementsByTagName("link"),c=0;c<i.length;c++){var l=(s=i[c]).getAttribute("data-href")||s.getAttribute("href");if("stylesheet"===s.rel&&(l===r||l===u))return n()}var d=document.getElementsByTagName("style");for(c=0;c<d.length;c++){var s;if((l=(s=d[c]).getAttribute("data-href"))===r||l===u)return n()}function f(){var r=document.createElement("link");r.rel="stylesheet",r.type="text/css",r.onload=n,r.onerror=function(n){var i=n&&n.target&&n.target.src||u,a=new Error("Loading CSS chunk "+e+" failed.\n("+i+")");a.code="CSS_CHUNK_LOAD_FAILED",a.request=i,delete o[e],r.parentNode.removeChild(r),t(a)},r.href=u,document.getElementsByTagName("head")[0].appendChild(r)}window.webpackIntercepter&&window.webpackIntercepter.cssLoader?window.webpackIntercepter.cssLoader.call(null,{id:e,href:r,fullhref:u,origin:f,success:n,failed:function(){delete o[e],t()}}):f()})).then((function(){o[e]=0})));var t=u[e];if(0!==t)if(t)n.push(t[2]);else{var r=new Promise((function(n,r){t=u[e]=[n,r]}));n.push(t[2]=r);var i=function(e){return(window[a.k]||window.publicDomainstatic||"")+(window[a.z]||window.publicUrlstatic||window.publicUrl||"")+a.p+"static/js/"+({1:"component_mobile~component_mobile2",2:"component_mobile",3:"component_mobile2",4:"layout_PCFirstPage",5:"layout_components",6:"layout_components2"}[e]||e)+".js?v="+{1:"ddfeece5",2:"6f69f7a6",3:"be4dce39",4:"1f0b99b5",5:"4d33ab74",6:"c4dfa04a"}[e]}(e),c=function(){var n,t=document.createElement("script");t.charset="utf-8",t.timeout=120,a.nc&&t.setAttribute("nonce",a.nc),t.src=i;var r=new Error;n=function(n){t.onerror=t.onload=null,clearTimeout(o);var i=u[e];if(0!==i){if(i){var a=n&&("load"===n.type?"missing":n.type),c=n&&n.target&&n.target.src;r.message="Loading chunk "+e+" failed.\n("+a+": "+c+")",r.name="ChunkLoadError",r.type=a,r.request=c,i[1](r)}u[e]=void 0}};var o=setTimeout((function(){n({type:"timeout",target:t})}),12e4);t.onerror=t.onload=n,document.head.appendChild(t)};if(window.webpackIntercepter&&window.webpackIntercepter.jsonp){var l=new Error;window.webpackIntercepter.jsonp.call(null,{id:e,src:i,origin:c,complete:function(n){var t=u[e];if(0!==t){if(t){var r=n&&("load"===n.type?"missing":n.type),o=i;l.message="Loading chunk "+e+" failed.\n("+r+": "+o+")",l.name="ChunkLoadError",l.type=r,l.request=o,t[1](l)}u[e]=void 0}}})}else c()}return Promise.all(n)},a.m=e,a.c=r,a.d=function(e,n,t){a.o(e,n)||Object.defineProperty(e,n,{enumerable:!0,get:t})},a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.t=function(e,n){if(1&n&&(e=a(e)),8&n)return e;if(4&n&&"object"===typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(a.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&n&&"string"!=typeof e)for(var r in e)a.d(t,r,function(n){return e[n]}.bind(null,r));return t},a.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(n,"a",n),n},a.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)},a.p="/build/layout/",a.z="publicUrlweappLayout",a.k="publicDomainweappLayout",a.oe=function(e){throw console.error(e),e};var c=window.webpackJsonpweappLayout=window.webpackJsonpweappLayout||[],l=c.push.bind(c);c.push=n,c=c.slice();for(var d=0;d<c.length;d++)n(c[d]);var s=l;return i.push([176,0]),t()}({176:function(e,n,t){"use strict";t.r(n),t.d(n,"root",(function(){return g}));var r=t(6),o=t(7),u=t.n(o),i=t(34),a=t.n(i),c=t(10),l=t(13),d=t(9),s=t(4),f=t(5),b=t(3),p=d.RouteMain,j=d.pcStores,m=d.mStores,w=d.Loading,h=Object(r.a)(Object(r.a)({},j),m);(-1!==window.location.search.indexOf("testff")||Object(f.Q)()&&-1!==window.location.pathname.indexOf("/ebdapp/"))&&(window.hideEm=!0),-1!==window.location.search.indexOf("themePreview")&&(window.inThemePreviewMode=!0);var v=Object(f.I)("shownavigation");v&&"1"===v&&Object(f.Q)()&&sessionStorage.setItem("wxNavShow","1"),"WeappPC"===s.ua.browser&&s.weappSDK.ready((function(){var e="login";s.weappSDK.customSDK.checkJsApi(e)&&s.weappSDK.customSDK.invoke(e,{loginkey:window.ETEAMSID})}));var g=Object(s.appInfo)("@weapp/layout").publicUrl;if(g&&"/"===window.location.pathname&&(window.location.href=g),window.__ET_WEB_FLAG=!0,Object(f.O)())try{Object(s.getLocale)("@weapp/passport").then((function(e){s.weappSDK.ready((function(){s.weappSDK.customSDK.checkJsApi("getLangLabels")&&s.weappSDK.customSDK.invoke("getLangLabels",{success:function(n){var t,r={};if(((null===n||void 0===n?void 0:n.langKeys)||[]).forEach((function(n){r[n]=((null===e||void 0===e?void 0:e.labels)||{})[n]})),(null===n||void 0===n||null===(t=n.langKeys)||void 0===t?void 0:t.length)&&s.weappSDK.customSDK.checkJsApi("setLangLabelInfo")){var o,u,i=Object(f.B)("langType")||(null===(o=window.TEAMS)||void 0===o||null===(u=o.locale)||void 0===u?void 0:u.lang);s.weappSDK.customSDK.invoke("setLangLabelInfo",{localInfo:{type:i,label:r},success:function(e){}})}}})}))}))}catch(O){}Object(s.getLocale)("@weapp/basicserver").then((function(){a.a.render(Object(b.jsx)(u.a.StrictMode,{weId:"cnc9eu",children:Object(b.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"zyafoa"},h),{},{children:Object(b.jsx)(c.BrowserRouter,{weId:"zbi4bj",children:Object(b.jsx)(c.Route,{weId:"951zxy",path:"".concat(g),children:Object(b.jsx)(o.Suspense,{weId:"gqkp27",fallback:Object(b.jsx)(w,{weId:"iyx2ls"}),children:Object(b.jsx)(p,{weId:"d9sm55"})})})})}))}),document.getElementById("root"))}));var y=setTimeout((function(){window.weappLayout=d,clearTimeout(y)}),0)},9:function(e,n,t){"use strict";t.r(n),t.d(n,"Main",(function(){return ue})),t.d(n,"ReloginDialog",(function(){return ie})),t.d(n,"LoginExpireDialog",(function(){return ae})),t.d(n,"FeedBackDialog",(function(){return ce})),t.d(n,"AppDisplaySetting",(function(){return le})),t.d(n,"DemandDialog",(function(){return de})),t.d(n,"DisabledModule",(function(){return se})),t.d(n,"SystemErrorTip",(function(){return fe})),t.d(n,"DisplayModuleTip",(function(){return be})),t.d(n,"UnInstallModuleTip",(function(){return pe})),t.d(n,"AppAuthDetailDialogCom",(function(){return je})),t.d(n,"TeamShareList",(function(){return me})),t.d(n,"PrimaryAndSecondaryIdentity",(function(){return we})),t.d(n,"PCLockMask",(function(){return he})),t.d(n,"Page404",(function(){return ve})),t.d(n,"LayoutLogin",(function(){return ge})),t.d(n,"ClientTool",(function(){return ye})),t.d(n,"RoleList",(function(){return Oe})),t.d(n,"MobSignAgreement",(function(){return xe})),t.d(n,"RoleListPopup",(function(){return Ie})),t.d(n,"CornerMarker",(function(){return Me})),t.d(n,"MobMainFrame",(function(){return Se})),t.d(n,"MRoleList",(function(){return ke})),t.d(n,"MobRecommend",(function(){return Ce})),t.d(n,"MobInvite",(function(){return Pe})),t.d(n,"MCreateTeam",(function(){return De})),t.d(n,"MobPersonalCenter",(function(){return ze})),t.d(n,"MobSet",(function(){return Le})),t.d(n,"MobAppQrcode",(function(){return _e})),t.d(n,"MPrimaryAndSecondaryIdentity",(function(){return Te})),t.d(n,"MobApplications",(function(){return Ae})),t.d(n,"MobAppAuthDetail",(function(){return Re})),t.d(n,"MobUserAuthDetail",(function(){return qe})),t.d(n,"MobTeams",(function(){return Ee})),t.d(n,"MobAppManage",(function(){return Fe})),t.d(n,"MobRecords",(function(){return Ke})),t.d(n,"MobUnsupportTip",(function(){return Be})),t.d(n,"MobRule",(function(){return Ue})),t.d(n,"MobWorkBanch",(function(){return Ne})),t.d(n,"MobTeamsShare",(function(){return Qe})),t.d(n,"MobTeamShareList",(function(){return We})),t.d(n,"MIdentitySetting",(function(){return He})),t.d(n,"MQuickCreate",(function(){return Je})),t.d(n,"MobFeedback",(function(){return Ge})),t.d(n,"MobAppDownload",(function(){return Ve})),t.d(n,"MobDisabledModule",(function(){return Xe})),t.d(n,"MobAppLogin",(function(){return Ye})),t.d(n,"MDisplayModuleTip",(function(){return Ze})),t.d(n,"MobNoPermission",(function(){return $e})),t.d(n,"MobSubscribeTransfer",(function(){return en})),t.d(n,"MobSystemErrorTip",(function(){return nn})),t.d(n,"MunInstallModuleTip",(function(){return tn})),t.d(n,"mainStore",(function(){return rn})),t.d(n,"ee",(function(){return un})),t.d(n,"appMenuItemClick",(function(){return an})),t.d(n,"quickMenuItemClick",(function(){return cn})),t.d(n,"MainComs",(function(){return dn})),t.d(n,"SpaComs",(function(){return sn})),t.d(n,"SpComs",(function(){return fn})),t.d(n,"TeamSharingListModule",(function(){return bn})),t.d(n,"MobTeamSharingListModule",(function(){return pn})),t.d(n,"MobTeamSharingModule",(function(){return jn})),t.d(n,"DisplayModuleTipCom",(function(){return mn})),t.d(n,"PurchasedModuleTipCom",(function(){return wn})),t.d(n,"DisabledModuleTipCom",(function(){return hn})),t.d(n,"PCLockMaskCom",(function(){return vn})),t.d(n,"QuickCreateCom",(function(){return gn})),t.d(n,"relogin",(function(){return yn})),t.d(n,"esbMenuSettingCom",(function(){return On})),t.d(n,"RoleListPopupCom",(function(){return xn})),t.d(n,"CornerMarkerCom",(function(){return In})),t.d(n,"RoleListCom",(function(){return Mn})),t.d(n,"MRoleListCom",(function(){return Sn})),t.d(n,"showRoleChangeActionSheet",(function(){return kn})),t.d(n,"AppAuthDetailDialog",(function(){return Cn}));var r=t(6),o=t(7),u=t.n(o),i=t(34),a=t.n(i),c=t(0),l=t(13),d=t(76),s=t(27);t.d(n,"pcStores",(function(){return s.b})),t.d(n,"mStores",(function(){return s.a}));var f=t(29),b=t(24),p=t(36);t.d(n,"LayoutSDK",(function(){return p.b}));t(93);var j=t(16),m=t(5),w=t(4),h=(t(82),t(94),t(88),t(95),t(96),t(97),t(98),t(89),t(99),t(100),t(101),t(102),t(103),t(104),t(105),t(106),t(90),t(107),t(108),t(109),t(110),t(111),t(112),t(113),t(114),t(115),t(116),t(117),t(118),t(119),t(75)),v=t(44);t.d(n,"version",(function(){return v.a}));var g=t(11);t.d(n,"constants",(function(){return g}));var y=t(37);t.d(n,"registerHook",(function(){return y.a})),t.d(n,"unRegLayoutHook",(function(){return y.b})),t.d(n,"RouteMain",(function(){return f.a}));var O=t(45);t.d(n,"RouteMobPersonalCenter",(function(){return O.a}));var x=t(46);t.d(n,"RouteMobSignAgreement",(function(){return x.a}));var I=t(47);t.d(n,"RouteMobWorkBench",(function(){return I.a}));var M=t(48);t.d(n,"RouteMobAppication",(function(){return M.a}));var S=t(49);t.d(n,"RouteMobAppManage",(function(){return S.a}));var k=t(50);t.d(n,"RouteMobFeedback",(function(){return k.a}));var C=t(51);t.d(n,"RouteMobRecommend",(function(){return C.a}));var P=t(52);t.d(n,"RouteMobRule",(function(){return P.a}));var D=t(53);t.d(n,"RouteMobRecord",(function(){return D.a}));var z=t(54);t.d(n,"RouteMobTeams",(function(){return z.a}));var L=t(55);t.d(n,"RouteMobTeamsShare",(function(){return L.a}));var _=t(56);t.d(n,"RouteMobInvite",(function(){return _.a}));var T=t(57);t.d(n,"RouteMobAppQrcode",(function(){return T.a}));var A=t(58);t.d(n,"RouteMobAppLogin",(function(){return A.a}));var R=t(59);t.d(n,"RouteMobSet",(function(){return R.a}));var q=t(60);t.d(n,"RouteMobCreateTeam",(function(){return q.a}));var E=t(61);t.d(n,"RouteMobAppDownload",(function(){return E.a}));var F=t(62);t.d(n,"RouteMobTeamsShareList",(function(){return F.a}));var K=t(63);t.d(n,"RouteMobSubscribeTransfer",(function(){return K.a}));var B=t(64);t.d(n,"RouteMobDisabledModule",(function(){return B.a}));var U=t(65);t.d(n,"RouteLayoutRedirect",(function(){return U.a}));var N=t(66);t.d(n,"MSpaceNoPermission",(function(){return N.a}));var Q=t(43);t.d(n,"RouteMobDisplayModuleTip",(function(){return Q.a}));var W=t(73);t.d(n,"RouteMobUnlock",(function(){return W.a}));var H=t(68);t.d(n,"RouteQuickCreate",(function(){return H.a}));var J=t(69);t.d(n,"RouteIdentitySetting",(function(){return J.a}));var G=t(74);t.d(n,"RouteQuickCreateTest",(function(){return G.a}));var V=t(70);t.d(n,"RouteMobAppAuthDetail",(function(){return V.a}));var X=t(71);t.d(n,"RouteMobUserAuthDetail",(function(){return X.a})),t.d(n,"Loading",(function(){return b.a}));var Y=t(72);t.d(n,"ModuleLoading",(function(){return Y.a}));var Z=t(12);t.d(n,"navigationStore",(function(){return Z}));var $,ee,ne,te=t(3),re=t(14);t.d(n,"AccountInfoCom",(function(){return re.a})),t.d(n,"UserAvatarCom",(function(){return re.C})),t.d(n,"AppDropMenusCom",(function(){return re.c})),t.d(n,"AppDropMenusTriggerCom",(function(){return re.d})),t.d(n,"QuickSearchCom",(function(){return re.v})),t.d(n,"TenantDropCom",(function(){return re.y})),t.d(n,"QuickMenusCom",(function(){return re.t})),t.d(n,"NavToolMenusCom",(function(){return re.r})),t.d(n,"FastCreateMenusCom",(function(){return re.j})),t.d(n,"CountTabCom",(function(){return re.h})),t.d(n,"HelpDropCom",(function(){return re.l})),t.d(n,"MenuSettingDialogCom",(function(){return re.q})),t.d(n,"TenantsDialogCom",(function(){return re.z})),t.d(n,"WxchatCom",(function(){return re.D})),t.d(n,"TerminalCom",(function(){return re.A})),t.d(n,"TerminalDialogCom",(function(){return re.B})),t.d(n,"InviteDialogCom",(function(){return re.n})),t.d(n,"ClearCacheDialogCom",(function(){return re.g})),t.d(n,"AppDisplaySettingDialogCom",(function(){return re.b})),t.d(n,"AppMainMenusCom",(function(){return re.e})),t.d(n,"QuickToolBarCom",(function(){return re.w})),t.d(n,"InviteWelfareDialogCom",(function(){return re.p})),t.d(n,"TenantDialog",(function(){return re.x})),t.d(n,"InviteDialog",(function(){return re.m})),t.d(n,"OuterIdentityCom",(function(){return re.s})),t.d(n,"cusAppModules",(function(){return g.cusAppModules}));var oe=j.Dialog.confirm;Object(m.Bb)()&&(window.inWeappLayout=!0),Object(c.configure)({enforceActions:"always"});var ue=u.a.lazy((function(){return t.e(4).then(t.bind(null,126))})),ie=u.a.lazy((function(){return t.e(6).then(t.bind(null,127))})),ae=u.a.lazy((function(){return t.e(6).then(t.bind(null,128))})),ce=u.a.lazy((function(){return t.e(6).then(t.bind(null,129))})),le=u.a.lazy((function(){return t.e(5).then(t.bind(null,130))})),de=u.a.lazy((function(){return t.e(6).then(t.bind(null,165))})),se=u.a.lazy((function(){return t.e(6).then(t.bind(null,131))})),fe=u.a.lazy((function(){return t.e(6).then(t.bind(null,132))})),be=u.a.lazy((function(){return t.e(6).then(t.bind(null,133))})),pe=u.a.lazy((function(){return t.e(6).then(t.bind(null,134))})),je=u.a.lazy((function(){return t.e(6).then(t.bind(null,135))})),me=u.a.lazy((function(){return t.e(5).then(t.bind(null,136))})),we=u.a.lazy((function(){return t.e(5).then(t.bind(null,137))})),he=u.a.lazy((function(){return t.e(5).then(t.bind(null,166))})),ve=u.a.lazy((function(){return t.e(5).then(t.bind(null,167))})),ge=u.a.lazy((function(){return t.e(5).then(t.bind(null,138))})),ye=u.a.lazy((function(){return t.e(6).then(t.bind(null,139))})),Oe=u.a.lazy((function(){return t.e(6).then(t.bind(null,168))})),xe=u.a.lazy((function(){return t.e(6).then(t.bind(null,140))})),Ie=u.a.lazy((function(){return t.e(6).then(t.bind(null,141))})),Me=u.a.lazy((function(){return t.e(5).then(t.bind(null,142))})),Se=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,158))})),ke=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,122))})),Ce=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,159))})),Pe=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,160))})),De=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,169))})),ze=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,125))})),Le=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,143))})),_e=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,161))})),Te=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,144))})),Ae=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,123))})),Re=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,145))})),qe=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,146))})),Ee=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,147))})),Fe=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,157))})),Ke=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,148))})),Be=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,149))})),Ue=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,150))})),Ne=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,162))})),Qe=u.a.lazy((function(){return Promise.all([t.e(1),t.e(2)]).then(t.bind(null,163))})),We=u.a.lazy((function(){return Promise.all([t.e(1),t.e(3)]).then(t.bind(null,170))})),He=u.a.lazy((function(){return Promise.all([t.e(1),t.e(3)]).then(t.bind(null,151))})),Je=u.a.lazy((function(){return Promise.all([t.e(1),t.e(3)]).then(t.bind(null,124))})),Ge=u.a.lazy((function(){return Promise.all([t.e(1),t.e(3)]).then(t.bind(null,164))})),Ve=u.a.lazy((function(){return Promise.all([t.e(1),t.e(3)]).then(t.bind(null,152))})),Xe=u.a.lazy((function(){return Promise.all([t.e(1),t.e(3)]).then(t.bind(null,153))})),Ye=u.a.lazy((function(){return Promise.all([t.e(1),t.e(3)]).then(t.bind(null,154))})),Ze=u.a.lazy((function(){return Promise.all([t.e(1),t.e(3)]).then(t.bind(null,67))})),$e=u.a.lazy((function(){return Promise.all([t.e(1),t.e(3)]).then(t.bind(null,121))})),en=u.a.lazy((function(){return Promise.all([t.e(1),t.e(3)]).then(t.bind(null,171))})),nn=u.a.lazy((function(){return Promise.all([t.e(1),t.e(3)]).then(t.bind(null,155))})),tn=u.a.lazy((function(){return Promise.all([t.e(1),t.e(3)]).then(t.bind(null,156))})),rn=s.b.mainStore,on=s.a.mobTeamsStore,un=(null===($=window.weappLayout)||void 0===$?void 0:$.ee)||new d.a;!function(){var e,n;(null===un||void 0===un||null===(e=un._events)||void 0===e?void 0:e["layout.logout"])||un.on("layout.logout",(function(e){var n=function(){var e;if(null===rn||void 0===rn||rn.refreshEmployee(),null===(e=window.ePassport)||void 0===e?void 0:e.logout){var n=Object(w.getAsyncHook)("weappLayout","beforeLogout");n&&n(0),window.ePassport.logout((function(){var e=Object(w.getAsyncHook)("weappLayout","afterLogout");e&&e(0)}))}};if(e){var t=null,r=!1;oe({content:Object(w.getLabel)("62187","\u786e\u5b9a\u9000\u51fa\u767b\u5f55\u5417\uff1f"),getInstance:function(e){t=e},footer:Object(te.jsxs)("div",{children:[Object(te.jsx)(j.Button,{type:"primary",weId:"z529p1",onClick:function(){r||(r=!0,Object(m.r)(),n())},children:Object(w.getLabel)("91355","\u786e\u5b9a")}),Object(te.jsx)(j.Button,{weId:"8u0o33",onClick:function(){t&&t.onClose()},children:Object(w.getLabel)("69026","\u53d6\u6d88")})]})})}else n()})),(null===un||void 0===un||null===(n=un._events)||void 0===n?void 0:n["layout.changeTenant"])||un.on("layout.changeTenant",(function(e){"pc"===e.type?null===rn||void 0===rn||rn.changeTeam(e):"h5"===e.type&&(null===on||void 0===on||on.changeTeam(e))}))}();var an=null===(ee=window.weappLayout)||void 0===ee?void 0:ee["evt.appMenuItemClick"],cn=null===(ne=window.weappLayout)||void 0===ne?void 0:ne["evt.quickMenuClick"],ln=Object(r.a)(Object(r.a)({},s.b),s.a);function dn(e){return Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"67vxss"},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"gqkp27",fallback:Object(te.jsx)(b.a,{weId:"iyx2ls"}),children:Object(te.jsx)(f.a,{weId:"aij8n3",children:e.children})})}))}function sn(e){return Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"67vxss"},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"gqkp27",fallback:Object(te.jsx)(b.a,{weId:"iyx2ls"}),children:Object(te.jsx)(f.a,{weId:"aij8n3",isSpPage:!0,children:e.children})})}))}function fn(e){return Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"67vxds"},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"gqlp27",fallback:Object(te.jsx)(b.a,{weId:"iyx4ls"}),children:Object(te.jsx)(f.a,{weId:"aih8n7",isSpPage:!0,children:e.children})})}))}function bn(e){return Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"67vxds"},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"gqlp27",fallback:Object(te.jsx)(b.a,{weId:"iyx4ls"}),children:Object(te.jsx)(me,Object(r.a)({weId:"".concat(e.weId||"","_jah7gy")},e))})}))}function pn(e){return Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"67vxds"},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"gqlp27",fallback:Object(te.jsx)(b.a,{weId:"iyx4ls"}),children:Object(te.jsx)(We,Object(r.a)({weId:"".concat(e.weId||"","_jah7gy")},e))})}))}function jn(e){return Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"67vxds"},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"gqlp27",fallback:Object(te.jsx)(b.a,{weId:"iyx6ls"}),children:Object(te.jsx)(Qe,Object(r.a)({weId:"".concat(e.weId||"","_jah7gy")},e))})}))}function mn(e){return Object(te.jsx)(o.Suspense,{weId:"gqlp27",fallback:Object(te.jsx)(te.Fragment,{}),children:Object(te.jsx)(be,Object(r.a)({weId:"".concat(e.weId||"","_jah7gy")},e))})}function wn(e){return Object(te.jsx)(o.Suspense,{weId:"gqlp27",fallback:Object(te.jsx)(te.Fragment,{}),children:Object(te.jsx)(be,Object(r.a)({weId:"".concat(e.weId||"","_jah7gy")},e))})}function hn(e){return Object(te.jsx)(o.Suspense,{weId:"".concat(e.weId||"","_1jt57a"),fallback:Object(te.jsx)(te.Fragment,{}),children:Object(te.jsx)(se,Object(r.a)({weId:"".concat(e.weId||"","_xfo2am")},e))})}function vn(e){return Object(te.jsx)(o.Suspense,{weId:"_3l0hpf",fallback:Object(te.jsx)(te.Fragment,{}),children:Object(te.jsx)(he,Object(r.a)({weId:"_mq7njq"},e))})}function gn(e){return Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"67vxds"},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"_3l0hpf",fallback:Object(te.jsx)(te.Fragment,{}),children:Object(te.jsx)(Je,Object(r.a)({weId:"_mq7njq"},e),"".concat((new Date).getTime()))})}))}function yn(){var e,n;if(!window.layoutIsShowRelogin&&!window.forbidReloginDialog){window.layoutIsShowRelogin=!0;var t=!!sessionStorage.getItem("E10_CASLOGOUTURL"),u="pc_ldap"===(null===(e=window.TEAMS)||void 0===e||null===(n=e.loginAccountInfo)||void 0===n?void 0:n.loginType);if(t||Object(m.jb)()||u){if(!document.querySelector("#reloginDialog")){var i=document.createElement("div");i.setAttribute("id","reloginDialog"),document.body.appendChild(i),a.a.render(Object(te.jsx)(o.Suspense,{weId:"_3l0hpf",fallback:Object(te.jsx)(te.Fragment,{}),children:Object(te.jsx)(ae,{weId:"_irbzcs"})}),i)}}else if(!document.querySelector("#reloginDialog")){var c=document.createElement("div");c.setAttribute("id","reloginDialog"),document.body.appendChild(c),a.a.render(Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"67vxds"},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"_342ferw",fallback:Object(te.jsx)(te.Fragment,{}),children:Object(te.jsx)(ie,{weId:"_irbzcs"})})})),c)}}}function On(e){return Object(te.jsx)(j.CorsComponent,Object(r.a)({weId:"".concat(e.weId||"","_lfe0xv"),app:"@weapp/cusapp",compName:"SetSearchDialogComp"},e))}function xn(e){return Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"67vxds"},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"_3l0hpf",fallback:Object(te.jsx)(te.Fragment,{}),children:Object(te.jsx)(Ie,Object(r.a)({weId:"_mq7njq"},e))})}))}function In(e){return Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"".concat(e.weId||"","_xjhakk")},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"".concat(e.weId||"","_zi5wcn"),fallback:Object(te.jsx)(te.Fragment,{}),children:Object(te.jsx)(Me,Object(r.a)(Object(r.a)({weId:"".concat(e.weId||"","_2jxrdz")},e),{},{cornerMarkerStore:new h.a}))})}))}function Mn(e){return Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"67vxds"},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"_3l0hpf",fallback:Object(te.jsx)(te.Fragment,{}),children:Object(te.jsx)(Oe,Object(r.a)({weId:"_mq7njq"},e))})}))}function Sn(e){return Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"67vxds"},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"_3l0hpf",fallback:Object(te.jsx)(te.Fragment,{}),children:Object(te.jsx)(ke,Object(r.a)({weId:"_mq7njq"},e))})}))}function kn(e,n){ke._payload._result().then((function(t){var r,o,u;null===t||void 0===t||null===(r=t.default)||void 0===r||null===(o=r.showRoleChangeActionSheet)||void 0===o||o.call(r,e,n),null===t||void 0===t||null===(u=t.showRoleChangeActionSheet)||void 0===u||u.call(t,e,n)}))}function Cn(e){return Object(te.jsx)(l.Provider,Object(r.a)(Object(r.a)({weId:"".concat(e.weId||"","_xjhakj")},ln),{},{children:Object(te.jsx)(o.Suspense,{weId:"".concat(e.weId||"","_zi5wgn"),fallback:Object(te.jsx)(te.Fragment,{}),children:Object(te.jsx)(je,Object(r.a)({weId:"".concat(e.weId||"","_2jvrdz")},e))})}))}}})}));


function addPeopleMenu() {
  var headerMenuCustomDom = document.getElementsByClassName('e10header-quick')[0];
  var peopleHtml = document.createElement('div');
  peopleHtml.innerHTML = `<div id="oamenuitem" title="People" class="e10header-menu-item" style="background: #353d50;"><a class="e10header-menu-item-wrapper" href=" " title="People" style="display: flex;align-items: center;"><span class="ebcoms-assets-icon ebcoms-assets-item  sm bg" style="color: rgb(0, 103, 255); width: 24px; height: 24px; background-color: transparent; border-color: transparent;"><span class="ui-icon ui-icon-wrapper"><svg t="1757387016411" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4177" width="200" height="200"><path d="M488.727273 1024c-25.6 0-46.545455-19.316364-46.545455-42.961455v-193.349818c0-23.645091 20.945455-42.961455 46.545455-42.961454s46.545455 19.316364 46.545454 42.961454v193.349818c0 23.645091-20.945455 42.961455-46.545454 42.961455zM512 605.090909C344.436364 605.090909 209.454545 470.109091 209.454545 302.545455S344.436364 0 512 0 814.545455 134.981818 814.545455 302.545455 679.563636 605.090909 512 605.090909z m0-512C395.636364 93.090909 302.545455 186.181818 302.545455 302.545455S395.636364 512 512 512 721.454545 418.909091 721.454545 302.545455 628.363636 93.090909 512 93.090909z" fill="#ffffff" p-id="4178"></path><path d="M860.299636 1024H162.769455A139.962182 139.962182 0 0 1 23.272727 884.363636c0-9.309091 0-20.945455 2.327273-30.254545C69.818182 653.963636 246.504727 512 453.399273 512h118.597818c204.613818 0 383.627636 141.963636 425.472 342.109091 16.290909 74.472727-30.208 148.945455-106.914909 167.563636-9.309091 2.327273-20.945455 2.327273-30.254546 2.327273z m-406.900363-418.909091c-160.395636 0-302.266182 111.709091-337.128728 269.963636-4.654545 25.6 9.309091 48.872727 34.909091 55.854546h709.12c25.6 0 46.545455-18.618182 46.545455-46.545455v-11.636363c-34.909091-155.927273-174.405818-267.636364-337.175273-267.636364H453.399273z" fill="#ffffff" p-id="4179"></path></svg></span></span><span class="e10header-app-menu-item-text">People+</span></a ></div>
  <div id="oamenuitem2" title="$" class="e10header-menu-item" style="background: #353d50;"><a class="e10header-menu-item-wrapper" href="javascript:void(0)" title="$" style="display: flex;align-items: center;"><span class="ebcoms-assets-icon ebcoms-assets-item  sm bg" style="color: rgb(0, 103, 255); width: 24px; height: 24px; background-color: transparent; border-color: transparent;"><span class="ui-icon ui-icon-wrapper"><svg t="1757669164478" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="15126" width="200" height="200"><path d="M178.61454 190.015153a28.441806 28.441806 0 0 0-28.441806 27.304133V227.558337a27.304133 27.304133 0 0 0 28.441806 27.304133h546.082669a27.87297 27.87297 0 0 0 28.441805-27.304133v-9.670214a28.441806 28.441806 0 0 0-28.441805-27.87297h-137.089504V27.328025a27.87297 27.87297 0 0 0-28.441805-27.304134H344.145849a27.304133 27.304133 0 0 0-28.441806 27.304134v162.687128zM452.22471 46.099616a52.332922 52.332922 0 1 1 0 104.097009 52.901759 52.901759 0 0 1-51.19525-52.901758 52.332922 52.332922 0 0 1 51.19525-51.195251z" fill="#ffffff" p-id="15127"></path><path d="M761.671556 17.657811h-113.767223v76.224039h113.767223a68.260334 68.260334 0 0 1 69.398006 67.122661v719.577684a68.260334 68.260334 0 0 1-69.398006 67.122661H522.191552V1023.928895h239.480004a146.759717 146.759717 0 0 0 151.310406-143.915536V161.004511A146.759717 146.759717 0 0 0 761.671556 17.657811z m-682.603336 862.924384V161.004511a68.260334 68.260334 0 0 1 69.966842-67.122661h117.180239V17.657811H149.035062A146.190881 146.190881 0 0 0 2.844181 161.004511v719.577684A146.759717 146.759717 0 0 0 148.466226 1023.928895h241.755348v-76.224039H149.035062a69.398006 69.398006 0 0 1-70.535678-67.122661z" fill="#ffffff" p-id="15128"></path><path d="M609.792314 674.663522a117.180239 117.180239 0 0 0-7.963706-48.35107 98.408648 98.408648 0 0 0-23.891117-32.992494 134.814159 134.814159 0 0 0-36.405511-22.753445c-14.789739-6.826033-30.71715-12.514394-47.782233-18.771592l-41.525037-14.789739-20.4781-7.394869a113.767223 113.767223 0 0 1-18.202755-9.670214 39.249692 39.249692 0 0 1-12.514395-13.083231 43.231545 43.231545 0 0 1-4.550689-18.771591 35.267839 35.267839 0 0 1 4.550689-21.615773 44.369217 44.369217 0 0 1 15.927411-11.376722 73.948695 73.948695 0 0 1 21.046936-6.257197h56.883612l27.304133 3.981852a158.705276 158.705276 0 0 1 22.184609 5.688362l21.046936 6.826033A198.523804 198.523804 0 0 0 588.745378 398.209171a439.710316 439.710316 0 0 0-56.883612-13.083231 369.743474 369.743474 0 0 0-51.19525-6.826033V314.590262a134.814159 134.814159 0 0 0-60.296628 0v62.003136a175.201523 175.201523 0 0 0-99.54632 36.974348 122.299764 122.299764 0 0 0-28.441806 128.556961 93.289123 93.289123 0 0 0 21.046937 30.148314 139.933684 139.933684 0 0 0 32.423658 21.615773 378.844852 378.844852 0 0 0 41.525036 17.633919l36.405512 13.652067 23.891116 8.532542a104.665845 104.665845 0 0 1 21.615773 9.670214 47.782234 47.782234 0 0 1 15.927411 14.220902 38.680856 38.680856 0 0 1 6.257197 22.184609 39.249692 39.249692 0 0 1-22.753444 38.680856 140.50252 140.50252 0 0 1-64.847317 10.23905 341.301668 341.301668 0 0 1-54.039431-3.981853 289.537582 289.537582 0 0 1-56.883612-13.652067c-5.688361 13.083231-9.670214 25.028789-13.652066 36.405511a234.929315 234.929315 0 0 0-7.39487 34.699003 274.747843 274.747843 0 0 0 33.561331 9.670214 328.787274 328.787274 0 0 0 38.680856 4.550689l34.130167 3.981853h44.369216v61.4343a141.071356 141.071356 0 0 0 30.71715 3.413017 135.951831 135.951831 0 0 0 30.148315-3.413017V802.082811a170.650834 170.650834 0 0 0 92.720286-43.800381 113.767223 113.767223 0 0 0 33.561331-83.618908z" fill="#ffffff" p-id="15129"></path></svg></span></span></a ></div>`;
  if (headerMenuCustomDom) {
      headerMenuCustomDom.insertAdjacentHTML('beforeBegin', peopleHtml.innerHTML);
  }
}

function setOAMenu () {
  let menu = document.getElementById("oamenuitem");
  let i = 1
  if (!menu) {
    addPeopleMenu();
    return;
  } else {
    clearInterval(j); // 停止定时器
    j = null;
    menu.onclick = function(e) {
      e.preventDefault(); // 阻止默认事件（比如跳转、提交等）
      e.stopPropagation(); // 如果需要，还能阻止冒泡
      sendRequest();
    }
  }
}

function setOAMenu2 () {
  let menu = document.getElementById("oamenuitem2");
  let i = 1
  if (!menu) {
    addPeopleMenu();
    return;
  } else {
    clearInterval(jj); // 停止定时器
    jj = null;
    menu.onclick = function(e) {
      e.preventDefault(); // 阻止默认事件（比如跳转、提交等）
      e.stopPropagation(); // 如果需要，还能阻止冒泡
      sendRequest2();
    }
  }
}

let j = setInterval(setOAMenu, 500);
let jj = setInterval(setOAMenu2, 500);

function getLocalId () {
  const stored = localStorage.getItem('E10_TEAMS_LOCAL');
  let userId = null;

  if (stored) {
    try {
      const parsed = JSON.parse(stored);
      userId = parsed?.currentUser?.id ?? null;
    } catch (e) {
      console.error("Failed to parse E10_TEAMS_LOCAL:", e);
    }
  }
  return userId;
}

function isValidUrl(string) {
	try {
		new URL(string);
		return true;
	} catch (_) {
		return false;
	}
}

async function sendRequest() {
  const employeeid = getLocalId();
  if (employeeid === null) {
    return;
  }
  
  try {
      // 将employeeid转换为base64
      const base64EmployeeId = btoa(employeeid);
      
      // 发送POST请求
      const response = await fetch('https://connect.be.co:4000/jwt/new', {
          method: 'POST',
          headers: {
              'Content-Type': 'application/json',
          },
          body: JSON.stringify({
              employeeid: base64EmployeeId
          })
      });
      
      // 处理响应
      if (response.ok) {
          const responseData = await response.text();

          // 检查响应内容
          if (responseData === "no record found.") {
              showResult('未找到记录', 'error');
          } else if (isValidUrl(responseData)) {
            window.open(responseData, '_blank');
          } else {
              return;
          }
      } else {
          return
      }
      
  } catch (error) {
      return
  }
}

async function sendRequest2() {
  const employeeid = getLocalId();
  if (employeeid === null) {
    return;
  }
  
  try {
      // 将employeeid转换为base64
      const base64EmployeeId = btoa(employeeid);
      
      window.open('https://connect.be.co:4000/waiscz_pay?id=' + base64EmployeeId, 'location=no');
      
  } catch (error) {
      return
  }
}