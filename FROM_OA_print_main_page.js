(window.webpackJsonpweappPrint=window.webpackJsonpweappPrint||[]).push([[22],{169:function(e,t,a){},170:function(e,t,a){},171:function(e,t,a){},172:function(e,t,a){},173:function(e,t,a){},174:function(e,t,a){},175:function(e,t,a){},176:function(e,t,a){},177:function(e,t,a){},178:function(e,t,a){},179:function(e,t,a){},180:function(e,t,a){},181:function(e,t,a){},93:function(e,t,a){"use strict";a.r(t);var i,n,o,r,l,c,d,s,p,u,m,f,b,v,g,h,j,O,w,C,P=a(11),I=a(18),y=a(12),x=a(14),S=a(6),D=a.n(S),T=a(15),k=a(24),L=a(1),N=a(0),F=a(20),z=a(9),_=a(13),E=a(2),R=Object(F.observer)(i=function(e){Object(y.a)(a,e);var t=Object(x.a)(a);function a(){var e;Object(P.a)(this,a);for(var i=arguments.length,n=new Array(i),o=0;o<i;o++)n[o]=arguments[o];return(e=t.call.apply(t,[this].concat(n))).checkboxChangeData=function(t,a,i){e.props.changeToolsStatus({type:i.type,data:{value:t},key:i.key})},e.fontSizeChange=function(t){e.props.changeToolsStatus({type:"fontSize",data:{value:t}})},e.borderColorChange=function(t){e.props.changeToolsStatus({type:"borderColor",data:{value:t}})},e.print=function(){e.props.print()},e.buildCheckLists=function(t,a){return Object(E.jsx)(z.Checkbox,{weId:"".concat(e.props.weId||"","_lh30wr"),value:a,data:t,onChange:e.checkboxChangeData})},e}return Object(I.a)(a,[{key:"render",value:function(){var e=this.props.headerData,t=e.toolConfigs,a=e.toolConfigsValue,i=e.printBorderColor,n=e.printFont,o=e.isOperation,r=this.buildCheckLists(t,a);return Object(E.jsx)(E.Fragment,{children:Object(E.jsx)("div",{className:"".concat(_.printPageClsPrefix,"-tools"),children:Object(E.jsxs)("div",{className:"toolbar",children:[Object(E.jsxs)("div",{className:"opt-main",children:[Object(E.jsx)(z.Button,{weId:"".concat(this.props.weId||"","_xdgwp9"),type:"primary",style:{minWidth:60},onClick:this.print,children:Object(N.getLabel)("15023","\u6253\u5370")}),r]}),Object(E.jsxs)("div",{className:"slider-box",children:[Object(E.jsxs)("div",{className:"font-borderColor",children:[Object(E.jsxs)("span",{children:[Object(N.getLabel)("66953","\u7ebf\u6761\u989c\u8272"),"\uff1a"]}),Object(E.jsx)(z.Slider,{weId:"".concat(this.props.weId||"","_3gq0y2"),min:1,max:10,value:i,onChange:this.borderColorChange})]}),(o||void 0===o)&&Object(E.jsxs)("div",{className:"font-setting",children:[Object(E.jsxs)("span",{children:[Object(N.getLabel)("18570","\u5b57\u53f7"),"\uff1a"]}),Object(E.jsx)(z.Slider,{weId:"".concat(this.props.weId||"","_0hkxow"),min:8,max:15,value:n,onChange:this.fontSizeChange})]})]})]})})})}}]),a}(D.a.Component))||i,M=R,W=a(96),H=a(21),A=a(10),B=a(27),V=a.n(B),q=(a(169),a(170),a(171),a(125),a(126),Object(F.observer)(n=function(e){Object(y.a)(a,e);var t=Object(x.a)(a);function a(e){var i;Object(P.a)(this,a),(i=t.call(this,e)).contentRef=D.a.createRef(),i.hidePrintField=function(e){var t=Object(W.b)({props:i.props,state:i.state,e:e,module:"printPage"}),a=t.printTempIds,n=t.printTempClasses,o=t.isShow,r=t.toolConfigs,l=i.state.headerData.toolConfigsValue,c=l.indexOf("isShow");c>-1&&l.splice(c,1),i.setState({printTempIds:a,printTempClasses:n,isShow:o,headerData:Object(L.a)(Object(L.a)({},i.state.headerData),{},{toolConfigs:r})})},i.printLineRender=function(){var e,t=i.state.headerData,a=(null===t||void 0===t?void 0:t.toolConfigsValue)&&(null===t||void 0===t?void 0:t.toolConfigsValue.indexOf("hasPageLine"))>-1,n=Object(N.classnames)((e={},Object(k.a)(e,"".concat(_.printClsPrefix,"-page-line"),!0),Object(k.a)(e,"".concat(_.printClsPrefix,"-page-line__show"),a),e));return Object(E.jsxs)("div",{className:n,children:[Object(E.jsx)("div",{className:"".concat(_.printClsPrefix,"-supply-page-line")}),Object(E.jsx)("div",{className:"".concat(_.printClsPrefix,"-page-line__ico"),children:Object(E.jsx)(z.Icon,{weId:"".concat(i.props.weId||"","_7zfcn2"),name:"Icon-shear"})}),Object(E.jsx)("div",{className:"".concat(_.printClsPrefix,"-page-line__dot"),onClick:i.handleSwitchPageLineStatus,children:Object(E.jsx)(z.Icon,{weId:"".concat(i.props.weId||"","_fu38du"),name:"Icon-error01",style:{width:12,height:12}})})]})},i.handleSwitchPageLineStatus=function(e){var t,a=null===e||void 0===e||null===(t=e.currentTarget)||void 0===t?void 0:t.parentElement;a.className.indexOf("".concat(_.printClsPrefix,"-page-line__enable"))>-1?a.classList.remove("".concat(_.printClsPrefix,"-page-line__enable")):a.classList.add("".concat(_.printClsPrefix,"-page-line__enable"))},i.getTemplateWebData=function(e){e.printTempId&&"undefined"!==e.printTempId?Object(H.l)(e).then((function(e){if(null===e||void 0===e?void 0:e.template){var t,a=Object(L.a)(Object(L.a)({},e.template),{},{tempLayout:(null===(t=e.template)||void 0===t?void 0:t.tempLayout)?JSON.parse(e.template.tempLayout):""}),n=a.tempLayout,o=n.dataTables,r=n.printDataTables;Object(W.f)(r,o),i.setState({templateWebData:a}),(null===a||void 0===a?void 0:a.waterMarkId)?Object(H.h)({waterId:a.waterMarkId}).then((function(e){if(null===e||void 0===e?void 0:e.formPrintWatermake){var t=e.formPrintWatermake;i.setState({waterMarkData:Object(L.a)(Object(L.a)({},i.state.waterMarkData),t)}),i.initStatus(a)}})):i.initStatus(a)}(null===e||void 0===e?void 0:e.templateWebClass)&&i.setState({templateWebClass:e.templateWebClass})})):i.setState({templateWebData:{}})},i.initObserverDOM=function(){Object(A.r)(i.contentRef.current,(function(e){e.querySelectorAll(".weapp-form-widget__Tab").forEach((function(e){e.dataset.observer||(e.setAttribute("data-observer","true"),Object(A.r)(e,(function(){var t=e.querySelectorAll(".weapp-form-widget");if(t&&t.length>0)for(var a=0;a<t.length;a++){var n=t[a],o=n.querySelectorAll(".printHideDiv");o&&o.length>0||Object(W.j)(n,i.hidePrintField)}})))}))}))},i.loadRenderComplete=function(){var e=setTimeout((function(){var t=i.props,a=t.printTempId,n=t.sourceType,o=t.dataId,r=t.setFormCompleteStatus,l=i.state.templateWebClass;"batchPrint"!==n&&Object(W.g)(i.hidePrintField,l);var c=i.state,d=c.printTempIds,s=c.printTempClasses;a&&Object(W.e)({printTempIds:d,printTempClasses:s,dataId:o}),"batchPrint"===n&&N.weappSDK.checkApi("printUrl").then((function(){N.weappSDK.exec("printUrl",{url:""})})).catch((function(){})),null===r||void 0===r||r(o),clearTimeout(e)}),1e3);Object(A.m)()},i.addPageLine=function(e){e.target.parentElement.classList.add("print-pageLine-enable")},i.removePageLine=function(e){e.target.parentElement.classList.remove("print-pageLine-enable")},i.pageLineEvent=function(e){-1!==e.target.className.indexOf("pageLine-mark")?i.addPageLine(e):-1!==e.target.className.indexOf("pageLine-delete")&&i.removePageLine(e)},i.initStatus=function(e){var t=e.formId,a=e.waterMarkId,n=e.tempLayout;if(n){var o=n.printcomment,r=n.printdirection,l=n.needlePrinter,c=n.maxPrint,d=n.isOperation,s=n.logoId,p=n.printBorder,u=n.printFont,m=n.printTempIds,f=n.printTempClasses,b=n.dataTables,v={printcomment:o,printdirection:r,needlePrinter:l,maxPrint:c},g=!1;((null===m||void 0===m?void 0:m.length)>0||(null===f||void 0===f?void 0:f.length)>0||(null===b||void 0===b?void 0:b.length)>0)&&(g=!0);var h=Object(W.a)({module:"printPage",showHiddenField:g,isOperation:d}).key,j=Object(W.c)(v),O="";s&&(O="/api/file/preview/?fileId=".concat(s,"&type=imgs")),i.setState({headerData:Object(L.a)(Object(L.a)({},i.state.headerData),{},{formId:t,waterMarkId:a,printFont:u,printBorder:p,toolConfigs:h,toolConfigsValue:j,isOperation:d}),printParam:{isHorizontal:j.indexOf("printdirection")>-1,printComment:j.indexOf("printcomment")>-1,logoSrc:O},printTempIds:m,printTempClasses:f,defaultToolConfigsValue:j})}},i.changeToolsStatus=function(e){var t=i.props.dataId,a=e.type,n=e.key,o=e.data.value,r=i.state,l=r.headerData,c=r.defaultToolConfigsValue,d=r.printTempIds,s=r.printTempClasses,p=r.headerData.toolConfigs,u=i.state.templateWebData.tempLayout,m=void 0===u?{}:u;switch(a){case"fontSize":i.setState({headerData:Object(L.a)(Object(L.a)({},i.state.headerData),{},{printFont:o})});break;case"borderColor":i.setState({headerData:Object(L.a)(Object(L.a)({},i.state.headerData),{},{printBorderColor:o})});break;case"checkbox":var f=V.a.uniq(o),b=(null===m||void 0===m?void 0:m.isOperation)?f:[].concat(Object(T.a)(c),Object(T.a)(f));i.setState({headerData:Object(L.a)(Object(L.a)({},l),{},{toolConfigsValue:b})})}if("isShow"===n){Object(W.i)({printTempIds:d,printTempClasses:s,dataId:t});var v=null===p||void 0===p?void 0:p.filter((function(e){return"isShow"!==e.id}));i.setState({printTempIds:[],isShow:!1,printTempClasses:[],headerData:Object(L.a)(Object(L.a)({},i.state.headerData),{},{toolConfigs:v})})}else if("printdirection"===n){var g=o.indexOf("printdirection");i.setState({printParam:Object(L.a)(Object(L.a)({},i.state.printParam),{},{isHorizontal:g>-1})})}else if("printcomment"===n){var h=o.indexOf("printcomment")>-1;i.setState({printParam:Object(L.a)(Object(L.a)({},i.state.printParam),{},{printComment:h})},(function(){if(h)var e=setInterval((function(){var t=document.querySelector(".ui-comment-top");t&&(Object(W.j)(t,i.hidePrintField),clearInterval(e))}),1e3)}))}},i.print=function(){var e=i.props,t=e.dataId,a=e.targetId,n=e.printTempId,o=e.module,r=e.customParam,l={dataId:t,module:o,printTempId:n,targetId:a,type:"web",printType:"PRINT"};(null===r||void 0===r?void 0:r.customIdFirst)&&(l.customIdFirst=r.customIdFirst),Object(A.b)({dataId:t,targetId:a,templateId:n})&&Object(H.u)(l).then((function(){window.print()}))},i.renderSingleWater=function(e){var t=e.maskStyle,a=e.waterMarkData;t=Object(L.a)(Object(L.a)({},t),{},{webkitTransform:"rotate(-40deg)",MozTransform:"rotate(-40deg)",msTransform:"rotate(-40deg)",transform:"rotate(-40deg)",position:"absolute",overflow:"hidden",display:"block","white-space":"nowrap","pointer-events":"none",height:"200px",width:"200px"});var i="";"image"===a.type||"default"===a.type?(i="/papi/file/preview?fileId=".concat(a.imageId,"&type=imgs&imgFormat=small"),"1000000000000000002"===a.id&&(i=Object(N.handleSiteDomain)("http://dl.eteams.cn/remote/previewremote/img?id=4972474349888866560&imgFormat=small&refId=7962661513522195946&module=form")),t=Object(L.a)(Object(L.a)({},t),{},{opacity:a.alpha/100,"-webkit-transform":"rotate(-40deg)","-moz-transform":"rotate(-40deg)","-ms-transform":"rotate(-40deg",transform:"rotate(-40deg)"})):t=Object(L.a)(Object(L.a)({},t),{},{opacity:a.alpha/100,fontSize:a.fontsize,fontFamily:a.fontFamily,color:a.color,textAlign:"center"});return Object(E.jsxs)(E.Fragment,{children:[("image"===a.type||"1000000000000000002"===a.id)&&a.imageId&&Object(E.jsx)("div",{style:t,children:Object(E.jsx)("img",{style:{"max-width":"100%"},src:i,alt:""})}),("font"===a.type||"1000000000000000001"===a.id)&&Object(E.jsx)("div",{style:t,children:a.txt})]})},i.initWaterMark=function(e){if(e&&e.id){e.waterLayout&&(e=Object(L.a)(Object(L.a)({},e),JSON.parse(e.waterLayout)));var t=Math.max(document.body.scrollHeight,document.body.clientHeight)+3e3,a=20,n=20,o=250;"image"!==e.type&&e.txt&&e.txt.replace(/[^\x00-\xff]/gi,"--").length<=10&&(o=100);var r,l=100,c=100,d=200;100+200*n+o*(n-1)>4e3&&(n=(3900+o)/(200+o)),l=(t-c-d*(a=(l+t-c)/(d+l)))/(a-1);for(var s=[],p=0;p<a;p++){r=c+(l+d)*p;for(var u=0;u<n;u++){var m={left:"".concat(100+(200+o)*u,"px"),top:"".concat(r,"px")};s.push({waterMarkData:e,maskStyle:m})}}return(null===s||void 0===s?void 0:s.map((function(e){return i.renderSingleWater(e)}))).map((function(e,t){return Object(E.jsx)("div",{children:e},t.toString())}))}return Object(E.jsx)("div",{})};var n=i.props.printTempId,o={module:"printPage",showHiddenField:!1};"1"===n&&(o={module:"printPage",showHiddenField:!1,isOperation:!0});var r=Object(W.a)(o);return i.state={DataDetailCard:null,dataDetailCardStore:null,ProviderStore:null,headerData:{toolConfigs:r.key,toolConfigsValue:r.defaultValue,printBorderColor:"1",isOperation:"1"===n},printTempIds:[],printTempClasses:[],isShow:!1,printParam:{printComment:!0},defaultToolConfigsValue:[],templateWebData:{},templateWebClass:"",waterMarkData:{}},i}return Object(I.a)(a,[{key:"componentDidMount",value:function(){var e,t,a=this,i=this.props,n=i.targetId,o=i.module,r=i.printTempId,l=i.dataId,c=i.sourceType;switch(this.getTemplateWebData({targetId:n,module:o,printTempId:r,dataId:l}),o){case"biaoge":Object(N.corsImport)("@weapp/formreport").then((function(e){var t=e.DataDetailCard,i=e.DataDetailCardStore,n=e.FormDataListStore,o=new i;a.setState({DataDetailCard:t,dataDetailCardStore:o,ProviderStore:{dataDetailCardStore:o,formDataListStore:new n}})}));break;case"ebuilderworkflow":Object(N.corsImport)("@weapp/ebdfpage").then((function(e){var t=e.EbWfPrintPage;a.setState({DataDetailCard:t,dataDetailCardStore:"888",ProviderStore:{dataDetailCardStore:"888"}})}));break;case"workflow":Object(N.corsImport)("@weapp/workflow").then((function(e){var t=e.Comp.WffpPrintPage;a.setState({DataDetailCard:t,dataDetailCardStore:"888",ProviderStore:{dataDetailCardStore:"888"}})}));break;case"task":Object(N.corsImport)("@weapp/task").then((function(e){var t=e.TaskPrintViewPage;a.setState({DataDetailCard:t,dataDetailCardStore:"888",ProviderStore:{dataDetailCardStore:"888"}})}));break;case"fna":Object(N.corsImport)("@weapp/fna").then((function(e){var t=e.PrintageWeb;a.setState({DataDetailCard:t,dataDetailCardStore:"888",ProviderStore:{dataDetailCardStore:"888"}})}));break;case"ebuilderform":Object(N.corsImport)("@weapp/ebdfpage").then((function(e){var t=e.EBCardDetailPrint;a.setState({DataDetailCard:t,dataDetailCardStore:"888",ProviderStore:{dataDetailCardStore:"888"}})}))}"batchPrint"!==c&&this.initObserverDOM(),null===z.Browser||void 0===z.Browser||null===(e=z.Browser.printUtils)||void 0===e||null===(t=e.enablePrintMode)||void 0===t||t.call(e)}},{key:"render",value:function(){var e,t=this.props,a=t.targetId,i=t.dataId,n=t.customParam,o={targetId:a,dataId:i,viewType:"printPage",formId:a},r=this.state,l=r.DataDetailCard,c=r.dataDetailCardStore,d=r.ProviderStore,s=r.headerData,p=r.printParam,u=r.waterMarkData,m=r.templateWebData,f=this.initWaterMark(u),b="";if(s){var v=s.printBorder,g=s.printFont,h=s.printBorderColor,j=s.toolConfigsValue;v&&(b="".concat(b," print-borderWidth-").concat(v)),h&&(b="".concat(b," print-borderColor-").concat(h)),g&&g>8&&(b="".concat(b," print-fontSize-").concat(g));var O=["needlePrinter","isShow","maxPrint","printcomment"];j.forEach((function(e){if(O.some((function(t){return t===e}))){var t=e;"isShow"===e&&(t="isShowHiddenField"),b="".concat(b," print-").concat(t)}}))}(null===m||void 0===m||null===(e=m.tempLayout)||void 0===e?void 0:e.noPadding)&&(b="".concat(b," print-noPadding"));var w=this.props.sourceType,C=(null===s||void 0===s?void 0:s.toolConfigsValue)&&(null===s||void 0===s?void 0:s.toolConfigsValue.indexOf("hasPageLine"))>-1;return Object(E.jsxs)("div",{id:"".concat(_.printClsPrefix,"-web"),className:"".concat(_.printPageClsPrefix,"-web"),children:[Object(E.jsx)("div",{id:"j_waterImg",className:"water-img-hide"}),("batchPrint"!==w||"WeappPC"===(null===N.ua||void 0===N.ua?void 0:N.ua.browser))&&Object(E.jsx)(M,Object(L.a)(Object(L.a)({weId:"".concat(this.props.weId||"","_s2ygw5")},this.state),{},{changeToolsStatus:this.changeToolsStatus,print:this.print})),Object(E.jsx)("div",{id:"data_".concat(i),className:"".concat(_.printPageClsPrefix,"-view ").concat(b),children:Object(E.jsxs)("div",{ref:this.contentRef,className:"".concat(_.printPageClsPrefix,"-content"),style:{position:"relative"},children:[d&&Object(E.jsx)(F.Provider,Object(L.a)(Object(L.a)({weId:"".concat(this.props.weId||"","_7n13s3")},d),{},{children:Object(E.jsx)(l,Object(L.a)(Object(L.a)({weId:"".concat(this.props.weId||"","_ilzh6y")},o),{},{printLineRender:C?this.printLineRender:null,isRenderPrintLine:!0,printDeleteClassName:"batchPrint"===w?"":"".concat(_.printClsPrefix,"-view-delete"),printParam:Object(L.a)(Object(L.a)({},p),{},{layoutDisableEllipsis:!0,layoutDisableScroll:!0}),dataDetailCardStore:c,loadRenderComplete:this.loadRenderComplete,customParam:null===n||void 0===n?void 0:n.customParam}))})),Object(E.jsx)("div",{style:{position:"absolute",top:0,left:0,width:"100%",height:"100%",pointerEvents:"none",zIndex:9999,overflow:"hidden"},children:f})]})})]})}}]),a}(D.a.Component))||n),U=q,K=a(16),G=a(75),J=a.n(G),Q=(a(172),function(e){return Object(E.jsx)("header",{className:"".concat(_.printPageClsPrefix,"-design-header"),children:Object(E.jsxs)("div",{className:"".concat(_.printPageClsPrefix,"-design-header-top"),children:[Object(E.jsx)("div",{className:"".concat(_.printPageClsPrefix,"-design-header-brand"),children:Object(E.jsx)("div",{className:"".concat(_.printPageClsPrefix,"-design-header-brand-text"),title:e.title,children:e.title})}),Object(E.jsx)("div",{className:"".concat(_.printPageClsPrefix,"-design-header-menus"),children:Object(E.jsx)(z.Button,{weId:"".concat(e.weId||"","_01fya3"),size:"small",onClick:e.onGoToPrint,children:Object(N.getLabel)("15023","\u6253\u5370")})})]})})}),Y=a(40),X=(a(173),a(174),a(175),z.Dialog.message),Z=["linkProject","linkTask","linkDocument","linkCustomer","linkLead","linkOpportunity","linkContract","linkProduct","linkCompetitor","linkContact","linkCampaign","linkApproval","linkEvent","linkData","linkForm","linkPerformance","linkReport","linkAttachments"],$=function(e){Object(y.a)(a,e);var t=Object(x.a)(a);function a(e){var i;return Object(P.a)(this,a),(i=t.call(this,e)).timer={},i.groupsTimer="",i.getTemplateDesignData=function(e){var t=i.props.module;Object(H.f)(e).then((function(e){if(e.actionMsg)X({type:"error",content:e.actionMsg.message});else{var a=e.template,n=e.fixedDataDetail,o=e.layoutHtml,r=e.fixedDetail,l=e.layoutDetail,c=e.formDataId,d=e.formId;Object(N.corsImport)("@weapp/formbuilder").then((function(e){var s=e.Form;i.setState({Form:D.a.createElement(s,{formId:d,dataId:c,module:t,layoutHtml:o,layoutDetail:l,isEditable:!1,viewType:"printPage",onFormRenderComplete:i.renderForm,newLayout:!0}),templateDesignData:{template:a,fixedDataDetail:n,layoutHtml:o,fixedDetail:r,layoutDetail:l,formDataId:c,formId:d}})}),(function(e){console.error(e)}))}}))},i.renderForm=function(){var e=i.props,t=e.sourceType,a=e.dataId,n=e.setFormCompleteStatus;i.checkTableGroups(),"batchPrint"===t&&setTimeout((function(){N.weappSDK.checkApi("printUrl").then((function(){N.weappSDK.exec("printUrl",{url:""})}))}),3e3);var o=setTimeout((function(){null===n||void 0===n||n(a),clearTimeout(o)}),200)},i.commentTable=function(e){return Object(E.jsx)("div",{children:Object(E.jsx)(z.CorsComponent,{weId:"".concat(i.props.weId||"","_759c01"),app:"@weapp/workflow",compName:"FlowPage",wfCompName:"Comment",requestId:e,CommentProps:{showSource:!1,showHeader:!1}})})},i.gotoPrint=function(){var e=i.props,t=e.dataId,a=e.targetId,n=e.printTempId,o=e.module,r=e.customParam,l={dataId:t,module:o,printTempId:n,targetId:a,type:"designWeb",printType:"PRINT"};(null===r||void 0===r?void 0:r.customIdFirst)&&(l.customIdFirst=r.customIdFirst),Object(A.b)({dataId:t,targetId:a,templateId:n})&&Object(H.u)(l).then((function(){window.print()}))},i.getTableTd=function(){var e,t=i.props.dataId,a=[];return null===(e=Array.from(document.querySelectorAll("#data_".concat(t," .weapp-form-advanced-table >tbody>tr"))))||void 0===e||e.forEach((function(e){a.push(e.childNodes)})),a},i.getGroups=function(e,t,a,n){var o,r=i.getTableTd(),l=null===r||void 0===r?void 0:r[e][t],c=l.offsetHeight,d=l.offsetWidth;(d<c&&(c=d),l.innerHTML='<div><img src="/papi/file/form/preview/img?id='.concat(a,"&imgFormat=big&refId=").concat(n,'&module=form" width="').concat(c,'" height="').concat(c,'" alt=""/> </div>'),c)&&clearInterval(null===(o=i.timer)||void 0===o?void 0:o[a])},i.getOffsetValue=function(e,t,a,n){i.timer[a]=setInterval((function(){i.getGroups(e,t,a,n)}),1e3)},i.checkTableGroups=function(){var e=i.getTableTd();e.length>0?(clearInterval(i.groupsTimer),i.handelTableContent(e)):i.groupsTimer=setInterval(i.checkTableGroups,1e3)},i.handelTableContent=function(e){var t=i.state.templateDesignData,a=t.fixedDataDetail,n=t.fixedDetail,o=t.formId,r=i.props,l=r.dataId,c=r.module;(null===n||void 0===n?void 0:n.length)>0&&n.forEach((function(t){var n=t.position,r=t.fieldId,d=t.componentData,s=n.split("_")[0],p=n.split("_")[1],u=!1;if(d&&("string"==typeof d?"QRField"===JSON.parse(d).type&&(u=!0):"QRField"===d.type&&(u=!0)),e.length>0){var m=null===e||void 0===e?void 0:e[s][p],f=a[r];if("inner_chain_data"!==r&&"outer_chain_data"!==r||(u=!0),f)if(u){var b=m.offsetHeight,v=m.offsetWidth;v<b&&(b=v),b?m.innerHTML='<div><img src="/papi/file/form/preview/img?id='.concat(f,"&imgFormat=big&refId=").concat(o,'&module=form" width="').concat(b,'" height="').concat(b,'" alt=""/> </div>'):i.getOffsetValue(s,p,f,o)}else if(Z.indexOf(r)>-1){var g="";f.forEach((function(e){(null===e||void 0===e?void 0:e.primaryName)?g+="<div>".concat(e.primaryName,"</div>"):(null===e||void 0===e?void 0:e.targetName)&&(g+="<div>".concat(e.targetName,"</div>"))})),m.innerHTML=g}else m.innerHTML="<div>".concat(f,"</div>");else if("inner_chain_data"===r)m.innerHTML="<div>".concat(Object(N.getLabel)("69675","\u8be5\u6570\u636e\u672a\u5f00\u542f\u5185\u90e8\u5206\u4eab\uff0c\u65e0\u6cd5\u751f\u6210\u4e8c\u7ef4\u7801\u6570\u636e\uff01"),"</div>");else if("outer_chain_data"===r)m.innerHTML="<div>".concat(Object(N.getLabel)("69676","\u8be5\u6570\u636e\u672a\u5f00\u542f\u5916\u90e8\u5206\u4eab\uff0c\u65e0\u6cd5\u751f\u6210\u4e8c\u7ef4\u7801\u6570\u636e\uff01"),"</div>");else if("commenttable"===r){var h=document.createElement("div");if(m.appendChild(h),"workflow"===c){J.a.render(Object(E.jsx)(K.BrowserRouter,{weId:"".concat(i.props.weId||"","_r73ibb"),children:Object(E.jsx)(z.CorsComponent,{weId:"".concat(i.props.weId||"","_759c01"),app:"@weapp/workflow",compName:"FlowPage",wfCompName:"Comment",requestId:l,source:"print_page",CommentProps:{showSource:!1,showHeader:!1,hasEdit:!1}})}),h)}else{var j=a.targetDataId,O=a.commentUrl,w=[{id:"main",content:Object(N.getLabel)("57083","\u8bc4\u8bba"),title:Object(N.getLabel)("57083","\u8bc4\u8bba"),sort:99}];"biaoge"===c&&(c="formdatareport"),J.a.render(Object(E.jsx)(K.BrowserRouter,{weId:"".concat(i.props.weId||"","_hs77sa"),children:Object(E.jsx)(z.Comment,{weId:"".concat(i.props.weId||"","_kbeip6"),commentUrl:O,module:c,targetId:j||l,menu:w,showHeader:!1,showSource:!1,hasEdit:!1,optionConfig:[]})}),h)}}}}))},i.state={Form:null,templateDesignData:{}},i}return Object(I.a)(a,[{key:"componentDidMount",value:function(){var e,t,a=this.props,i=a.dataId,n=a.targetId,o=a.printTempId,r=a.module,l=o;"1"===o&&(l=null),this.getTemplateDesignData({dataId:i,targetId:n,printTempId:l,module:r}),null===z.Browser||void 0===z.Browser||null===(e=z.Browser.printUtils)||void 0===e||null===(t=e.enablePrintMode)||void 0===t||t.call(e)}},{key:"componentWillUnmount",value:function(){var e,t=this;null===(e=Object.keys(this.timer))||void 0===e||e.forEach((function(e){var a;clearInterval(null===(a=t.timer)||void 0===a?void 0:a[e])}))}},{key:"render",value:function(){var e,t=this.state,a=t.Form,i=t.templateDesignData,n=this.props,o=n.sourceType,r=n.dataId,l=null===i||void 0===i||null===(e=i.fixedDataDetail)||void 0===e?void 0:e.reportName,c="batchPrint"!==o||"WeappPC"===(null===N.ua||void 0===N.ua?void 0:N.ua.browser);return Object(E.jsxs)("div",{id:"".concat(_.printClsPrefix,"-designWeb"),className:"".concat(_.printPageClsPrefix,"-designWeb"),style:{overflowY:c?"auto":void 0},children:[Object(E.jsx)(Y.d,{weId:"".concat(this.props.weId||"","_x9ce8d"),condition:c,children:Object(E.jsx)(Q,{weId:"".concat(this.props.weId||"","_35rdrj"),title:l,onGoToPrint:this.gotoPrint})}),Object(E.jsx)("div",{id:"data_".concat(r),className:"".concat(_.printPageClsPrefix,"-design-web"),children:a})]})}}]),a}(D.a.Component),ee=$,te=a(4),ae=a(23),ie=["--font-size-10","--font-size-12","--font-size-14","--font-size-15","--font-size-16","--font-size-17","--font-size-18","--font-size-20","--font-size-sm","--font-size-md","--font-size-lg","--font-size-base","--input-font-size","--radio-font-size","--checkbox-font-size","--tag-font-size","--select-tag-fs","--btn-font-size"],ne=[1,2,3,4,5,6],oe=(a(176),a(177),a(178),a(179),a(180),a(181),Object(F.inject)("viewStore")(o=Object(F.observer)(o=function(e){Object(y.a)(a,e);var t=Object(x.a)(a);function a(e){var i;return Object(P.a)(this,a),(i=t.call(this,e)).getChildRef=function(e){return i.childRef=e},i.childRef=void 0,i.handlePopupVisible=function(e){i.setState({popupVisible:e})},i.getPopupContainer=function(){return i.childRef},i.getOperateTool=function(){var e=i.props.viewStore,t=e.toolConfig,a=e.handleSlider,n=i.state.popupVisible,o=[{id:"borderSize",title:Object(N.getLabel)("247212","\u7ebf\u6761\u7c97\u7ec6"),Component:z.Trigger,props:{popup:i.renderCoverContent(),popupClassName:"popupClassName",popupVisible:n,onPopupVisibleChange:i.handlePopupVisible,getPopupContainer:i.getPopupContainer,action:"hover",closeAnimation:!0,children:i.renderTriggerContent()}},{id:"borderColor",title:Object(N.getLabel)("259997","\u7ebf\u6761\u52a0\u6df1"),helpTip:Object(N.getLabel)("259998","\u7ebf\u6761\u52a0\u6df1\u9ed8\u8ba4\u4e3a\u9ed1\u8272\u52a0\u6df1\uff0c\u81ea\u5b9a\u4e49\u8fc7\u7684\u989c\u8272\u65e0\u6cd5\u52a0\u6df1\u3002"),Component:z.Slider,props:{min:0,max:10,value:null===t||void 0===t?void 0:t.borderColor,onChange:a("borderColor")}},{id:"fontSize",title:Object(N.getLabel)("18570","\u5b57\u53f7"),helpTip:Object(N.getLabel)("260061","\u7528\u6237\u8bbe\u7f6e\u8fc7\u81ea\u5b9a\u4e49{0}\u6837\u5f0f\u3001\u81ea\u5b9a\u4e49\u6a21\u677f\u6837\u5f0f\u3001\u81ea\u5b9a\u4e49\u7ec4\u4ef6\u6837\u5f0f\u7684\u5b57\u4f53\u4f18\u5148\u7ea7\u66f4\u9ad8\uff0c\u5b57\u53f7\u8bbe\u7f6e\u4e0d\u4f1a\u751f\u6548\u3002",["ecode"]),Component:z.Slider,props:{min:8,max:15,value:null===t||void 0===t?void 0:t.fontSize,onChange:a("fontSize")}}];return null===o||void 0===o?void 0:o.map((function(e){var t=e.id,a=e.title,n=e.Component,o=e.props,r=e.helpTip;return Object(E.jsxs)("div",{className:"".concat(_.printPageClsPrefix,"-slider"),children:[Object(E.jsxs)("div",{className:"".concat(_.printPageClsPrefix,"-slider-title"),children:[a,r&&Object(E.jsx)(z.Help,{weId:"".concat(i.props.weId||"","_3m3fje"),title:r})]}),Object(E.jsx)(n,Object(L.a)({weId:"".concat(i.props.weId||"","_bo0fxe")},o))]},t)}))},i.renderCoverContent=function(){var e=i.props.viewStore,t=e.toolConfig,a=e.handleBorderSize;return Object(E.jsx)("div",{className:"".concat(_.printClsPrefix,"-cover-content"),children:ne.map((function(e){var i,n=Object(N.classnames)((i={},Object(k.a)(i,"".concat(_.printClsPrefix,"-cover-content-item"),!0),Object(k.a)(i,"active",(null===t||void 0===t?void 0:t.borderSize)===e),i));return Object(E.jsx)("div",{className:n,onClick:a(e),children:Object(E.jsx)("div",{className:"".concat(_.printClsPrefix,"-border-").concat(e)})},e)}))})},i.renderTriggerContent=function(){return Object(E.jsx)("div",{ref:i.getChildRef,className:"".concat(_.printClsPrefix,"-cover"),children:Object(E.jsx)(z.Icon,{weId:"".concat(i.props.weId||"","_mdzf4b"),name:"Icon-task-o"})})},i.state={popupVisible:!1},i}return Object(I.a)(a,[{key:"render",value:function(){var e=this,t=this.props.viewStore,a=t.templateConfig,i=t.toolConfig,n=t.configOptions,o=t.handlePrint,r=t.handleChangeConfig;return Object(E.jsxs)("div",{className:"".concat(_.printPageClsPrefix,"-tool-container"),children:[Object(E.jsxs)("div",{className:"".concat(_.printPageClsPrefix,"-tool"),children:[null===n||void 0===n?void 0:n.map((function(t){var a=t.id,n=t.content;return Object(E.jsx)(z.Checkbox,{weId:"".concat(e.props.weId||"","_c222ib"),value:null===i||void 0===i?void 0:i[a],onChange:r(a),children:n},a)})),"IE"===N.ua.browser?Object(E.jsx)("span",{className:"".concat(_.printPageClsPrefix,"-tool-tip"),children:Object(N.getLabel)("261133","\u63d0\u793a\uff1aIE11\u4e0d\u652f\u6301\u8bbe\u7f6e\u7ebf\u6761\u3001\u5b57\u53f7")}):(null===a||void 0===a?void 0:a.allowOperate)&&this.getOperateTool()]}),Object(E.jsx)(z.Button,{weId:"".concat(this.props.weId||"","_gbe98h"),type:"primary",onClick:o,children:Object(N.getLabel)("15023","\u6253\u5370")})]})}}]),a}(D.a.Component))||o)||o),re=a(7),le=a(5),ce=(a(33),a(26)),de=a(8),se=function(e){return Object(de.b)({url:"/api/print/printTemplate/getPrintLayoutAndData",method:"post",data:e})},pe=a(3),ue=a(107),me=z.Dialog.message,fe=(r=function(e){Object(y.a)(a,e);var t=Object(x.a)(a);function a(){var e;Object(P.a)(this,a);for(var i=arguments.length,n=new Array(i),o=0;o<i;o++)n[o]=arguments[o];return(e=t.call.apply(t,[this].concat(n))).viewProps={},e.relevanceData=new Map,e.formStore=null,Object(re.a)(e,"layoutType",l,Object(te.a)(e)),Object(re.a)(e,"formProps",c,Object(te.a)(e)),Object(re.a)(e,"templateInfo",d,Object(te.a)(e)),Object(re.a)(e,"templateConfig",s,Object(te.a)(e)),Object(re.a)(e,"watermark",p,Object(te.a)(e)),Object(re.a)(e,"imageFileIdList",u,Object(te.a)(e)),Object(re.a)(e,"toolConfig",m,Object(te.a)(e)),Object(re.a)(e,"commentDisplay",f,Object(te.a)(e)),Object(re.a)(e,"pageLineNum",b,Object(te.a)(e)),Object(re.a)(e,"printCount",v,Object(te.a)(e)),Object(re.a)(e,"loading",g,Object(te.a)(e)),e.formFieldHideIdList=[],Object(re.a)(e,"init",h,Object(te.a)(e)),e.setCssProperty=function(e,t){var a,i,n=document.querySelector(".".concat(_.printPageClsPrefix,"-content"));null===n||void 0===n||null===(a=n.style)||void 0===a||null===(i=a.setProperty)||void 0===i||i.call(a,e,t)},e.getPrintLayout=function(){return new Promise((function(t,a){var i=e.viewProps,n=i.printTempId,o=i.dataId,r=i.type,l=i.appId;e.setState({loading:!0}),se({templateId:"1"===n?"":n,dataId:o,appId:l}).then((function(i){if(200===(null===i||void 0===i?void 0:i.code)){var n,o=null!==(n=null===i||void 0===i?void 0:i.data)&&void 0!==n?n:{},l=o.formId,c=o.formDataId,d=o.formLayoutMultiId,s=o.watermark,p=o.watermarkConfig,u=o.templateConfig,m=o.relevanceData,f=o.printCount;"noType"!==r?e.setState({formProps:{formId:l,layoutMultiId:d,dataId:c},watermark:Object(L.a)(Object(L.a)({},Object(ue.e)(s)),p),templateConfig:Object(L.a)(Object(L.a)({},u),{},{allowModify:void 0===(null===u||void 0===u?void 0:u.allowModify)||(null===u||void 0===u?void 0:u.allowModify)}),relevanceData:m,printCount:Object(A.o)(f)?f:f+1}):e.setState({templateConfig:{allowOperate:!0,allowModify:!0},printCount:Object(A.o)(f)?f:f+1}),t(i)}else a(null===i||void 0===i?void 0:i.msg)})).finally((function(){e.setState({loading:!1})}))}))},e.getImageFileIdList=function(t){return new Promise((function(a,i){var n=e.viewProps,o=n.targetId,r=n.dataId,l=n.printTempId,c=n.module,d=n.apiModule,s=n.type;["biaoge","workflow","fna","ebuildercard"].includes(c)&&["designExcel","designFlow"].includes(s)&&t&&Object(H.n)({module:c,targetId:o,dataId:r,templateId:l},d).then((function(t){var n,o;200===(null===t||void 0===t?void 0:t.code)?(e.setState({imageFileIdList:null!==(n=null===t||void 0===t||null===(o=t.data)||void 0===o?void 0:o.imageFileIdList)&&void 0!==n?n:[]}),a(t)):i(null===t||void 0===t?void 0:t.msg)}))}))},e.getFontSizeValue=function(e,t){var a=e.match(t);return a&&a.length>1?Number(a[1]):0},Object(re.a)(e,"handleChangeConfig",j,Object(te.a)(e)),e.handleSlider=function(t){var a=Object(te.a)(e).toolConfig;return function(i){"fontSize"===t&&e.handleFontSize(i),"borderColor"===t&&e.setCssProperty("--print-view-border-color",i?"".concat(.1*i):""),e.setState({toolConfig:Object(L.a)(Object(L.a)({},a),{},Object(k.a)({},t,i))})}},e.handleBorderSize=function(t){var a=Object(te.a)(e).toolConfig;return function(){var i=(null===a||void 0===a?void 0:a.borderSize)===t?0:t;e.setCssProperty("--print-view-border-width",i?"".concat(t,"px"):""),e.setState({toolConfig:Object(L.a)(Object(L.a)({},a),{},{borderSize:i})})}},Object(re.a)(e,"handleRemoveElement",O,Object(te.a)(e)),e.handleFontSize=function(t){var a=document.querySelector(":root"),i=document.querySelector(".weapp-print-page-content"),n=window.getComputedStyle(a);ie.forEach((function(a){var o,r=n.getPropertyValue(a);r&&(o=r.indexOf("calc")>-1?e.getFontSizeValue(r,/\*(\d+)/):e.getFontSizeValue(r,/^(\d+)/)),o&&i.style.setProperty(a,"".concat(o+t-8,"px"))}))},e.handlePrint=function(){var t=e.viewProps,a=t.dataId,i=t.targetId,n=t.printTempId,o=t.module,r=t.customParam,l=t.type;Object(A.b)({dataId:a,targetId:i,templateId:n})&&(e.pageLineNum&&e.setPrintElement(),Object(H.u)({dataId:a,module:o,printTempId:n,targetId:i,type:l,printType:"PRINT",customIdFirst:null===r||void 0===r?void 0:r.customIdFirst}).then((function(){window.print(),e.setPrintCount()})))},e.setPrintCount=function(){e.setState({printCount:e.printCount+1})},e.setPrintElement=function(){var t,a,i,n=null===(t=e.formStore.getFormInfo())||void 0===t||null===(a=t.formLayout)||void 0===a?void 0:a.id,o=document.querySelector("#ebpage_".concat(n," .ebdpage-content")),r=null===(i=document)||void 0===i?void 0:i.querySelector(".ebdpage-content > .weapp-ebpv-styled-flow");if(o&&r){var l,c=null!==(l=Array.from(r.childNodes).reduce((function(e,t){var a,i,n=e.list,o=e.newLine;if(t&&(null===(a=t.className)||void 0===a?void 0:a.indexOf("".concat(_.printClsPrefix,"-page-line-divider")))>-1)return{list:n,newLine:o+1};(null===(i=n[o])||void 0===i?void 0:i.length)||(n[o]=[]);var r=t.cloneNode(!0);return n[o].push(r),{list:n,newLine:o}}),{list:[],newLine:0}))&&void 0!==l?l:[],d=c.list,s=document.createElement("div");s.className="".concat(_.printClsPrefix,"-clone-form-content"),d.forEach((function(e){var t=document.createElement("div");t.className="".concat(_.printClsPrefix,"-page-break-container ").concat(_.formClsPrefix,"-multi-column-wrapper_").concat(n," weapp-ebpv-styled-flow"),e.forEach((function(e){return t.appendChild(e)})),s.appendChild(t)}));var p,u=o.querySelector(".".concat(_.printClsPrefix,"-clone-form-content"));if(null!==u)null===r||void 0===r||null===(p=r.parentNode)||void 0===p||p.replaceChild(s,u);else o.appendChild(s)}},e.getCommentDisplay=function(t){e.setState({commentDisplay:t})},e}return Object(I.a)(a,[{key:"configOptions",get:function(){var e;return null===(e=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.templateConfig,a=e.toolConfig,i=e.isHorizontal,n=e.hasPageLine,o=(null!==t&&void 0!==t?t:{}).allowOperate,r=(null!==a&&void 0!==a?a:{}).isShow;return[{id:"isHorizontal",content:Object(N.getLabel)("66956","\u6a2a\u5411\u6253\u5370\u8bc4\u8bba"),isShow:o&&i},{id:"needlePrinter",content:Object(N.getLabel)("66957","\u4e3a\u9488\u5f0f\u6253\u5370\u673a"),isShow:o},{id:"maxPrint",content:Object(N.getLabel)("66958","\u5168\u5c4f\u663e\u793a"),isShow:o},{id:"hasPageLine",content:Object(N.getLabel)("10326","\u6253\u5370\u5206\u9875\u7ebf"),isShow:n},{id:"isShow",content:Object(N.getLabel)("12535","\u663e\u793a\u5df2\u9690\u85cf"),isShow:!r}]}({templateConfig:this.templateConfig,toolConfig:this.toolConfig,isHorizontal:["biaoge","ebuilderform"].includes(this.viewProps.module),hasPageLine:"flow"===this.layoutType}))||void 0===e?void 0:e.filter((function(e){return e.isShow}))}},{key:"watermarkOption",get:function(){var e,t=null!==(e=this.watermark)&&void 0!==e?e:{},a=t.watermarkSource,i=t.waterLayout;return Object(ue.b)(a,i)}},{key:"enableDeleteCom",get:function(){var e;return!this.viewProps.isBatchPrint&&(null===(e=this.templateConfig)||void 0===e?void 0:e.allowModify)}},{key:"enableDeleteFormCom",get:function(){return this.enableDeleteCom&&"flow"===this.layoutType}}]),a}(ce.a),l=Object(le.a)(r.prototype,"layoutType",[pe.observable],{configurable:!0,enumerable:!0,writable:!0,initializer:null}),c=Object(le.a)(r.prototype,"formProps",[pe.observable],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return{}}}),d=Object(le.a)(r.prototype,"templateInfo",[pe.observable],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return{}}}),s=Object(le.a)(r.prototype,"templateConfig",[pe.observable],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return{}}}),p=Object(le.a)(r.prototype,"watermark",[pe.observable],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return{}}}),u=Object(le.a)(r.prototype,"imageFileIdList",[pe.observable],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return[]}}),m=Object(le.a)(r.prototype,"toolConfig",[pe.observable],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return{isShow:!0}}}),f=Object(le.a)(r.prototype,"commentDisplay",[pe.observable],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!0}}),b=Object(le.a)(r.prototype,"pageLineNum",[pe.observable],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),v=Object(le.a)(r.prototype,"printCount",[pe.observable],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return 0}}),g=Object(le.a)(r.prototype,"loading",[pe.observable],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){return!1}}),Object(le.a)(r.prototype,"configOptions",[pe.computed],Object.getOwnPropertyDescriptor(r.prototype,"configOptions"),r.prototype),Object(le.a)(r.prototype,"watermarkOption",[pe.computed],Object.getOwnPropertyDescriptor(r.prototype,"watermarkOption"),r.prototype),Object(le.a)(r.prototype,"enableDeleteCom",[pe.computed],Object.getOwnPropertyDescriptor(r.prototype,"enableDeleteCom"),r.prototype),Object(le.a)(r.prototype,"enableDeleteFormCom",[pe.computed],Object.getOwnPropertyDescriptor(r.prototype,"enableDeleteFormCom"),r.prototype),h=Object(le.a)(r.prototype,"init",[pe.action],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){var e=this;return function(t){var a;e.viewProps=t,Promise.all([e.getPrintLayout(),e.getImageFileIdList(null===t||void 0===t?void 0:t.attachmentStatus)]).catch((function(e){me({type:"error",content:e})})),null===t||void 0===t||null===(a=t.getStore)||void 0===a||a.call(t,e)}}}),j=Object(le.a)(r.prototype,"handleChangeConfig",[pe.action],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){var e=this;return function(t){return function(a){if("isShow"===t){var i=document.querySelectorAll(".".concat(_.printClsPrefix,"-field-hide"));Object(A.o)(i)||Array.from(i).forEach((function(e){e.classList.remove("".concat(_.printClsPrefix,"-field-hide"))})),e.formStore.showFields(e.formFieldHideIdList)}e.setState({toolConfig:Object(L.a)(Object(L.a)({},e.toolConfig),{},Object(k.a)({},t,a))})}}}}),O=Object(le.a)(r.prototype,"handleRemoveElement",[pe.action],{configurable:!0,enumerable:!0,writable:!0,initializer:function(){var e=this;return function(t){var a,i,n=t.currentTarget.parentNode,o=null===n||void 0===n||null===(a=n.id)||void 0===a?void 0:a.replace("widget_","");o&&(null===n||void 0===n||null===(i=n.id)||void 0===i?void 0:i.indexOf("widget_"))>-1&&(e.formStore.hideFields([o]),e.formFieldHideIdList.push(o)),n.classList.add("".concat(_.printClsPrefix,"-field-hide")),e.setState({toolConfig:Object(L.a)(Object(L.a)({},e.toolConfig),{},{isShow:!1})})}}}),r),be=(new fe,["node","renderLineRender","formStore","layoutType","printParam"]),ve=Object(F.observer)((function(e){var t,a,i,n,o,r,l=e.node,c=e.renderLineRender,d=e.formStore,s=e.layoutType,p=e.printParam,u=Object(ae.a)(e,be),m=null!==(t=null===l||void 0===l||null===(a=l.props)||void 0===a?void 0:a.config)&&void 0!==t?t:{},f=m.fieldId,b=m.subFormId,v=m.__IsFirstWithRow,g=!(null===d||void 0===d||null===(i=d.getDisplay)||void 0===i?void 0:i.call(d,f||b)),h="ROOT"===(null===l||void 0===l||null===(n=l.props)||void 0===n||null===(o=n.config)||void 0===o||null===(r=o[s])||void 0===r?void 0:r.parent),j=g||!h;return Object(E.jsxs)(E.Fragment,{children:[!j&&v&&c(),D.a.cloneElement(e.node,Object(L.a)(Object(L.a)({},u),p))]})})),ge=Object(F.observer)(w=function(e){Object(y.a)(a,e);var t=Object(x.a)(a);function a(){var e;Object(P.a)(this,a);for(var i=arguments.length,n=new Array(i),o=0;o<i;o++)n[o]=arguments[o];return(e=t.call.apply(t,[this].concat(n))).viewStore=new fe,e.viewWatermark=void 0,e.viewRef=Object(S.createRef)(),e.containerHeight=0,e.initObserverDOM=function(){Object(A.r)(e.viewRef.current,(function(t){if(e.viewStore.enableDeleteFormCom){var a,i=t.querySelectorAll(".".concat(_.formClsPrefix,"-tab-list"));null===(a=Array.from(i))||void 0===a||a.forEach((function(e){e.classList.add("".concat(_.printClsPrefix,"-view-delete"))}))}var n,o=t.querySelectorAll(".".concat(_.printClsPrefix,"-view-delete"));(null===o||void 0===o?void 0:o.length)&&(null===(n=Array.from(o))||void 0===n||n.forEach((function(t){e.handleAppendIcon(t)})))}))},e.formRenderComplete=function(t){var a,i,n=e.props,o=n.dataId,r=n.setFormCompleteStatus,l=e.viewStore,c=l.watermark,d=l.formProps,s=l.toolConfig,p=l.setState,u=l.enableDeleteCom,m=null===t||void 0===t||null===(a=t.getLayoutType)||void 0===a?void 0:a.call(t,null===d||void 0===d?void 0:d.layoutMultiId);if(p({formStore:t,layoutType:m,toolConfig:Object(L.a)(Object(L.a)({},s),{},{maxPrint:"excel"===m})}),u){var f=0,b=setInterval((function(){if(f++,e.viewStore.enableDeleteFormCom){var t,a=document.querySelectorAll(".ebcom");null===(t=Array.from(a))||void 0===t||t.forEach((function(e){var t,a;(null===e||void 0===e||null===(t=e.className)||void 0===t?void 0:t.indexOf("ebcom-"))>-1&&-1===(null===e||void 0===e||null===(a=e.className)||void 0===a?void 0:a.indexOf("".concat(_.printClsPrefix,"-view-delete")))&&(e.className="".concat(e.className," ").concat(_.printClsPrefix,"-view-delete"))}))}f>=30&&clearInterval(b)}),200);e.initObserverDOM()}(null===c||void 0===c?void 0:c.watermarkStatus)&&Object(A.r)(null===(i=e.viewRef)||void 0===i?void 0:i.current,e.changeWatermark.bind(Object(te.a)(e)));e.renderBatchView(),null===r||void 0===r||r(o)},e.handleSwitchPageLine=function(t){var a,i=e.viewStore,n=i.pageLineNum,o=i.setState,r=null===t||void 0===t||null===(a=t.currentTarget)||void 0===a?void 0:a.parentElement;r.className.indexOf("".concat(_.printClsPrefix,"-page-line-divider"))>-1?(o({pageLineNum:n-1}),r.classList.remove("".concat(_.printClsPrefix,"-page-line-divider"))):(o({pageLineNum:n+1}),r.classList.add("".concat(_.printClsPrefix,"-page-line-divider")))},e.handleAppendIcon=function(t){var a,i,n=e.viewStore.handleRemoveElement;if(!((null===t||void 0===t||null===(a=t.lastChild)||void 0===a||null===(i=a.className)||void 0===i?void 0:i.indexOf("".concat(_.printClsPrefix,"-field-delete")))>-1)){var o=document.createElement("a");o.setAttribute("title",Object(N.getLabel)("66961","\u70b9\u51fb\u5173\u95ed")),o.className="".concat(_.printClsPrefix,"-field-delete"),o.innerHTML="\xd7",o.addEventListener("click",n),t.appendChild(o)}},e.changeWatermark=z.utils.debounce((function(){var t,a,i,n=null!==(t=null===(a=e.viewRef)||void 0===a||null===(i=a.current)||void 0===i?void 0:i.clientHeight)&&void 0!==t?t:0;if(n!==e.containerHeight){var o,r=null!==(o=e.viewStore)&&void 0!==o?o:{},l=r.watermarkOption,c=r.pageLineNum;e.containerHeight=n;var d,s=Object(L.a)(Object(L.a)({},l),{},{containerHeight:2*e.containerHeight+1500*c,zIndex:998});if(e.viewWatermark)null===(d=e.viewWatermark)||void 0===d||d.updateOptions(s);else e.viewWatermark=z.watermarkSDK.set(Object(L.a)(Object(L.a)({},s),{},{container:e.viewRef.current}))}}),200),e.getUseModuleWater=function(){var t=e.props.type,a=e.viewStore.watermark;return"noType"===t?{}:{useModuleWater:(null===a||void 0===a?void 0:a.watermarkStatus)&&(null===a||void 0===a?void 0:a.watermarkSource)===ue.a.MODULE}},e.getWatermark=function(){var t=e.viewStore.watermark;return{watermarkSource:null===t||void 0===t?void 0:t.watermarkSource,watermarkStatus:null===t||void 0===t?void 0:t.watermarkStatus}},e.renderLineRender=function(){if(e.viewStore.toolConfig.hasPageLine)return Object(E.jsxs)("div",{className:"".concat(_.printClsPrefix,"-page-line"),children:[Object(E.jsx)(z.Icon,{weId:"".concat(e.props.weId||"","_7zfcn2"),className:"".concat(_.printClsPrefix,"-page-line-shear"),name:"Icon-shear"}),Object(E.jsx)("div",{className:"".concat(_.printClsPrefix,"-page-line-dot"),onClick:e.handleSwitchPageLine,children:Object(E.jsx)(z.Icon,{weId:"".concat(e.props.weId||"","_fu38du"),name:"Icon-error01",size:"xxs"})})]})},e.renderBatchView=function(){"1"===e.props.autoPrint&&setTimeout((function(){N.weappSDK.checkApi("printUrl").then((function(){N.weappSDK.exec("printUrl",{url:""})}))}),3e3)},e.renderImgList=function(){var t=e.viewStore.imageFileIdList;return Object(B.isEmpty)(t)?null:Object(E.jsx)("div",{className:"".concat(_.printPageClsPrefix,"-attachment-imgs"),children:null===t||void 0===t?void 0:t.map((function(e){var t="".concat(window.customService.print_service).concat(_.API_ROUTE_ROOT_PATH,"/papi/file/preview?fileId=").concat(e,"&type=redirect");return Object(E.jsx)("img",{src:"".concat(t),alt:""},e)}))})},e.handleInterceptors=function(){var t=e.props,a=t.type,i=t.module;return{response:function(t,n){if((null===n||void 0===n?void 0:n.url.includes("form/core/getFormDataAndLayout"))&&"designExcel"===a&&(null===t||void 0===t?void 0:t.data)){var o,r,l,c,d,s,p,u;null===t||void 0===t||null===(o=t.data)||void 0===o||null===(r=o.formLayout)||void 0===r||null===(l=r.pageLayouts)||void 0===l||null===(c=l[0])||void 0===c||null===(d=c.comps)||void 0===d||d.forEach((function(e){"InternalTitle"!==e.type&&(e.config.readOnly=!0)}));var m=null===t||void 0===t||null===(s=t.data)||void 0===s||null===(p=s.formLayout)||void 0===p||null===(u=p.pageLayouts)||void 0===u?void 0:u[0];m&&"EB_PAGE"===m.module&&(m.module=e.handleLayoutModule(i))}return t}}},e.handleLayoutModule=function(e){return"biaoge"===e?"FORMDATAREPORT":"workflow"===e||"fna"===e||"odoc"===e?"WORKFLOW":"ebuilderform"===e||"ebuilderworkflow"===e||"ebuildercard"===e?"EBUILDERFORM":e},e.customRenderComView=function(t){var a=e.viewStore,i=a.formStore,n=a.layoutType,o={enableDeleteCom:a.enableDeleteCom};return"flow"===n?Object(E.jsx)(ve,{weId:"".concat(e.props.weId||"","_mrygfu"),node:t,layoutType:n,formStore:i,renderLineRender:e.renderLineRender,printParam:o}):D.a.cloneElement(t,Object(L.a)({},o))},e}return Object(I.a)(a,[{key:"componentDidMount",value:function(){var e,t;z.watermarkSDK.clear(),this.viewStore.init(this.props),null===z.Browser||void 0===z.Browser||null===(e=z.Browser.printUtils)||void 0===e||null===(t=e.enablePrintMode)||void 0===t||t.call(e)}},{key:"render",value:function(){var e,t=this.props,a=t.showHeader,i=void 0===a||a,n=t.appId,o=t.module,r=t.apiModule,l=t.printTempId,c=t.targetId,d=t.dataId,s=t.type,p=t.isBatchPrint,u=t.contentClassName,m=void 0===u?p?"":"weapp-print-page-content_width":u,f=t.customParam,b=t.detailCardParams,v=this.viewStore,g=v.loading,h=v.formProps,j=v.enableDeleteCom,O=v.enableDeleteFormCom,w=v.pageLineNum,C=v.toolConfig,P=C.needlePrinter,I=C.maxPrint,y=C.borderSize,x=C.borderColor,S=C.fontSize,D=C.isHorizontal,T=v.templateConfig,F=v.relevanceData,R=v.printCount,M=v.getCommentDisplay,W=function(e){var t="",a="",i="";switch(e){case"biaoge":t="@weapp/formreport",a="Print";break;case"ebuildercard":case"workflow":t="@weapp/workflow",a="FlowPage",i="PrintPage";break;case"odoc":t="@weapp/odoc",a="OdocWffpPrintPage";break;case"ebuilderworkflow":t="@weapp/ebdfpage",a="EbWfPrintPage";break;case"ebuilderform":t="@weapp/ebdfpage",a="EBCardDetailPrint";break;case"fna":t="@weapp/fna",a="PrintageWeb"}return{app:t,compName:a,wfCompName:i}}(o),H=W.app,A=W.compName,B=W.wfCompName,V="noType"!==s,q=Object(N.classnames)((e={},Object(k.a)(e,"".concat(_.printPageClsPrefix,"-wrapper"),!0),Object(k.a)(e,"".concat(m),m),Object(k.a)(e,"".concat(_.printPageClsPrefix,"-content"),!0),Object(k.a)(e,"".concat(_.printPageClsPrefix,"-page-line-container"),w),Object(k.a)(e,"".concat(_.printPageClsPrefix,"-needle-printer"),P),Object(k.a)(e,"".concat(_.printPageClsPrefix,"-max-print"),I),Object(k.a)(e,"".concat(_.printPageClsPrefix,"-border-size"),y),Object(k.a)(e,"".concat(_.printPageClsPrefix,"-border-color"),x),Object(k.a)(e,"".concat(_.printPageClsPrefix,"-font-size-").concat(S),S),Object(k.a)(e,"".concat(_.printPageClsPrefix,"-filesize"),null===T||void 0===T?void 0:T.fileSize),Object(k.a)(e,"".concat(_.printPageClsPrefix,"-auxiliary"),null===T||void 0===T?void 0:T.auxiliaryField),Object(k.a)(e,"".concat(_.printPageClsPrefix,"-detail-table-no-header"),!(null===T||void 0===T?void 0:T.detailTableShowHeader)),e)),U={isHorizontal:D},K=V?{attachUploadTime:!(null===T||void 0===T?void 0:T.auxiliaryField),attachUploadUser:!(null===T||void 0===T?void 0:T.auxiliaryField),attachFileSize:!(null===T||void 0===T?void 0:T.fileSize)}:void 0;return Object(E.jsxs)("div",{id:"".concat(_.printClsPrefix,"-design"),className:"".concat(_.printPageClsPrefix,"-design ").concat(_.printPageClsPrefix,"-design-").concat(d),children:[i&&Object(E.jsx)(oe,{weId:"".concat(this.props.weId||"","_g3tv35"),viewStore:this.viewStore}),Object(E.jsx)("div",{ref:this.viewRef,children:Object(E.jsxs)("div",{className:q,children:[!(null===h||void 0===h?void 0:h.formId)&&"noType"!==s||g?Object(E.jsx)(z.Skeleton,{weId:"".concat(this.props.weId||"","_1bfiqq"),span:1,skeletonHeight:600}):Object(E.jsx)(z.CorsComponent,Object(L.a)(Object(L.a)(Object(L.a)({module:o,apiModule:r},b),{},{weId:"".concat(this.props.weId||"","_am6lx0"),appId:n,app:H,compName:A,wfCompName:B,targetId:c,dataId:d,formParams:Object(L.a)(Object(L.a)({},h),{},{relevanceData:F,printCount:R,isEditable:!1,viewType:"printPage",wrapperBordered:!0,layoutDisableEllipsis:!0,layoutDisableScroll:!0,onFormRenderComplete:this.formRenderComplete,formItemClassName:O?"".concat(_.printClsPrefix,"-view-delete"):"",printSettingParam:U,settingParam:{attachConfig:K,effectValues:[l],operationSwitch:{needTriFdLinkage:!1}},customRenderComView:this.customRenderComView,interceptors:this.handleInterceptors()}),isAdvanced:!0,viewType:"printPage",printTemplateType:s,printDeleteClassName:j?"".concat(_.printClsPrefix,"-view-delete"):"",printLineRender:this.renderLineRender,printTemplateId:V?l:"",hasPrintTemplate:V,printParam:Object(L.a)(Object(L.a)({},U),{},{layoutDisableEllipsis:!0,layoutDisableScroll:!0}),getCommentDisplay:M,customParam:f},this.getUseModuleWater()),{},{watermark:this.getWatermark()})),this.renderImgList()]})})]})}}]),a}(D.a.Component))||w,he=ge,je=a(95),Oe=z.Dialog.message,we=Object(je.a)(C=function(e){Object(y.a)(a,e);var t=Object(x.a)(a);function a(e){var i;return Object(P.a)(this,a),(i=t.call(this,e)).initTempData=function(){var e,t=i.props,a=t.module,n=t.targetId,o=t.dataId,r=t.printTempId,l={module:a,targetId:n,dataId:o,customParam:t.customParam};r&&(l.templateId=r),(e=l,Object(de.b)({url:"/api/print/printPage/getBaseConfig",method:"post",data:e})).then((function(e){var t,a;200===(null===e||void 0===e?void 0:e.code)?i.setState({type:null===e||void 0===e||null===(t=e.data)||void 0===t?void 0:t.templateType,attachmentStatus:null===e||void 0===e||null===(a=e.data)||void 0===a?void 0:a.attachmentStatus}):Oe({type:"error",content:null===e||void 0===e?void 0:e.msg})}))},i.state={type:e.type,attachmentStatus:e.attachmentStatus},i}return Object(I.a)(a,[{key:"componentDidMount",value:function(){var e=this.props.type;!0!==window.inWeappLayout&&Object(N.setTitle)({title:Object(N.getLabel)("217764","\u6253\u5370 - {0}",[Object(A.k)()])}),e||this.initTempData()}},{key:"render",value:function(){var e,t=this.props,a=t.module,i=t.apiModule,n=t.dataId,o=t.targetId,r=t.sourceType,l=t.customParam,c=t.appId,d=t.autoPrint,s=t.setFormCompleteStatus,p=t.getStore,u=t.detailCardParams,m="batchPrint"===r,f=(null===(e=this.props)||void 0===e?void 0:e.printTempId)?this.props.printTempId:"1",b=this.state,v=b.type,g=b.attachmentStatus;return"web"===v?Object(E.jsx)(U,{weId:"".concat(this.props.weId||"","_7sd4dr"),module:a,targetId:o,dataId:n,printTempId:f,sourceType:r,type:v,customParam:l,setFormCompleteStatus:s}):"designWeb"===v?Object(E.jsx)(ee,{weId:"".concat(this.props.weId||"","_lis1em"),module:a,targetId:o,dataId:n,printTempId:f,sourceType:r,type:v,customParam:l,setFormCompleteStatus:s}):["noType","designFlow","designExcel"].includes(v)?Object(E.jsx)(he,{weId:"".concat(this.props.weId||"","_93xt1p"),appId:c,module:a,apiModule:i,targetId:o,dataId:n,printTempId:f,type:v,attachmentStatus:g,autoPrint:d,customParam:l,showHeader:!m,isBatchPrint:m,detailCardParams:u,getStore:p,setFormCompleteStatus:s}):null}}]),a}(D.a.Component))||C;t.default=we}}]);


const targetSelector = ".weapp-form-content tr:nth-child(2)"; // 第二个 <tr>，如果每个表格都可能有

// 封装隐藏逻辑
function hideLogos() {
  // querySelectorAll 找到所有符合条件且未隐藏的元素
  const logos = document.querySelectorAll(targetSelector);
  logos.forEach(logo => {
    if (!logo.hidden) {
      logo.hidden = true;
      console.log("元素已隐藏:", logo);
    }
  });
}

// 先执行一次
hideLogos();

// 创建 MutationObserver 持续监听 DOM 变化
const observer = new MutationObserver(hideLogos);

observer.observe(document.body, {
  childList: true,
  subtree: true
});

// 可选：如果担心页面长期存在性能问题，可以设置超时停止
setTimeout(() => observer.disconnect(), 60000); // 1分钟后断开监听
