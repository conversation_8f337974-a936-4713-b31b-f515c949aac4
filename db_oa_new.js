var db = {};
var mysql = require('mysql');
var pool = mysql.createPool({
    connectionLimit: 10,
    host: 'rm-j6c95u8ak3n7w3cc9wo.mysql.rds.aliyuncs.com',
    user: 'root',
    password: '5RGbetad^&CVDAZ',
    database: 'eteams'
});

db.query = function(sql, params) {
    return new Promise((resolve, reject)=>{
        pool.getConnection(function(err, connection) {
            if(err){
                console.log("建立连接失败:", err);
                reject(err);
                return;
            }
            
            connection.query(sql, params, function(err, rows, fields) {
                // 无论成功还是失败，都要释放连接回连接池
                connection.release();
                
                if(err) {
                    console.log("查询失败:", err);
                    reject(err);
                } else {
                    console.log(rows.length + ' rows');
                    resolve(rows);
                }
            });
        });
    });
}

// 添加连接池状态监控
db.getPoolStatus = function() {
    return {
        totalConnections: pool._allConnections.length,
        freeConnections: pool._freeConnections.length,
        acquiringConnections: pool._acquiringConnections.length
    };
}

// 添加获取数据库版本的方法
db.getVersion = function() {
    return this.query('SELECT VERSION() as version');
}

module.exports = db;



