var express = require('express');
var router = express.Router();
var db = require('../db.js');
var db_oa_new = require('../db_oa_new.js');
var fs = require('fs');

function format(num, cent) {
    return (num.toFixed(cent) + '').replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,');
}

function fromBase64(base64) {
    try {
      const binaryString = atob(base64);
      const bytes = Uint8Array.from(binaryString, char => char.charCodeAt(0));
      const decoder = new TextDecoder(); // 默认 UTF-8
      return decoder.decode(bytes);
    } catch (e) {
      console.error("Failed to decode Base64:", e);
      return null;
    }
  }

var last_year_month = function() {
	var d = new Date();
	
	var year = '';
	var month = '';

	d.setMonth(d.getMonth() - 12);
	var m = d.getMonth() + 1;
	m = m < 10 ? "0" + m : m;
	year = d.getFullYear();
	month = m;
	var result = year + '-' + month + '-01';
	return result;
}

router.get('/', function(req, res, next) {
    if (req.query.id.length <= 0) {
        return;
    }
    var filename = "custom/be_pay_new.json";
    fs.readFile(filename, {
        encoding: 'utf-8'
    }, function(err, data) {
        var sql2 = "";
        var p_yyyy_dd = last_year_month();
        var p_name = "";
        //var p_yyyy = p_yyyy_dd['year'];
        //var p_mm = p_yyyy_dd['month'];
        var id = fromBase64((req.query.id));
        if (err) throw err;
        var jsonObj = JSON.parse(data);
        for (var i = 0, size = jsonObj.length; i < size; i++) {
            var record = jsonObj[i];
            var name = record['name'];
            var sql_string = record['sql'];
            var sql = "SELECT ID as id, EMAIL as email FROM employee where ID = '" + id + "'";
            sql2 = sql_string;
            //sql2 = sql2.replace(/p_yyyy/g, p_yyyy);
            //sql2 = sql2.replace(/p_mm/g, p_mm);
            sql2 = sql2.replace(/p_yyyy_dd/g, p_yyyy_dd);
            
            p_name = name;

        }
        if (sql2.length == "") {
            res.send('Please indicate payroll name.')
        } else {
        	// 使用Promise方式调用db_oa_new.query
        	db_oa_new.query(sql).then(function(result_email) {
                if (result_email.length > 0) {
                	console.log(result_email);
                	sql2 = sql2.replace(/p_id/g, result_email[0].email);
                	

                    db.sql(sql2, function(err, result) {
		                if (err) {
		                    console.log(err);
		                    return;
		                }

		                if (result.length > 0) {

		                    p_name = result[0].Fullname + '  -  ' + result[0].fnumber;
                            //console.log(result);
		                    res.render('be_pay_new', {
		                        'data': result,
		                        'name': p_name
		                    });
		                } else {
		                    res.send('no record found.');
		                }
		            });

                } else {
                    res.send('no record found.');
                }
            }).catch(function(err) {
                console.log('Database query error:', err);
                res.status(500).send('Database query failed');
            });

            
        }
    });
});
module.exports = router;